<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>幸年堂 - 角色列表</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="shortcut icon" href="/dist/images/favicon.ico" type="image/x-icon">
    <link href="/dist/layui/css/layui.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="/dist/css/main.css">
    <script src="/dist/layui/layui.js"></script>
    <script src="/dist/js/main.js"></script>
    <style>
        body {
            width: 480px;
            padding: 30px 50px 0 50px;
        }

        #barcode_img {
            width: 80px;
            height: 80px;
        }

        #avatar_img {
            width: 80px;
            height: 80px;
        }

        #Department i {
            margin-right: 5px;
        }

        .preview-text {
            color: #1E9FFF;
            font-size: 12px;
            margin-left: 10px;
            cursor: pointer;
        }

        .preview-img {
            display: inline-block;
            margin-left: 20px;
        }
    </style>
</head>

<body>
    <form class="layui-form" lay-filter="form_edit" action="" onsubmit="return false">
        <div class="layui-form-item">
            <label class="layui-form-label">电话/帐号</label>
            <div class="layui-input-block">
                <input type="text" name="phone" required lay-verify="required" placeholder="请输入输入框内容" autocomplete="off"
                    class="layui-input" onkeyup='isPhone(this)'>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">密码</label>
            <div class="layui-input-block">
                <div class="layui-input-wrap">
                    <div class="layui-input-prefix layui-input-split">
                        <i class="layui-icon layui-icon-password"></i>
                    </div>
                    <input type="password" placeholder="不填即不更改密码" class="layui-input" name="pwd">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">姓名</label>
            <div class="layui-input-block">
                <input type="text" name="name" required lay-verify="required" placeholder="请输入输入框内容" autocomplete="off"
                    class="layui-input">
            </div>
        </div>

        <div class="layui-form-item" style="display: flex;align-items: center;">
            <label class="layui-form-label">头像上传</label>
            <button type="button" class="layui-btn layui-btn-normal" id="avatar_file">选择头像文件</button>
            <div class="preview-img">
                <img id="avatar_img">
            </div>
        </div>

        <div class="layui-form-item" style="display: flex;align-items: center;">
            <label class="layui-form-label">二维码</label>
            <button type="button" class="layui-btn layui-btn-normal" id="barcode_file">选择二维码图片</button>
            <div class="preview-img">
                <img id="barcode_img">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">部门/科室</label>
            <div class="layui-input-block">
                <button id="Department" class="layui-btn layui-btn-primary"
                    style="display: flex;min-width: 120px;align-items: center;justify-content: center;">
                    <i class='iconfont'>&#xe686;</i>
                    <div>选择选项</div>
                </button>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="number" name="sort" required lay-verify="required" placeholder="请输入输入框内容"
                    autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">帐号状态</label>
            <div class="layui-input-block">
                <input type="radio" name="status" value="1" title="启用" checked>
                <input type="radio" name="status" value="0" title="禁用">
            </div>
        </div>
        <div class="layui-form-item" style="margin-top: 20px;">
            <div class="layui-input-block form_bottom_button">
                <button class="layui-btn" lay-submit lay-filter="formSubmitBtn">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
    <script>
        layui.use(['form', 'upload', 'jquery', 'layer'], function () {
            var form = layui.form;
            var upload = layui.upload;
            var $ = layui.jquery;
            var layer = layui.layer;
            var dropdown = layui.dropdown;
            var index = parent.layer.getFrameIndex(window.name);
            var id = window.location.search.split('=')[1];
            var department_id;
            // 获取ID参数
            var id = window.location.search.split('=')[1];
            if (!id) {
                layer.msg('参数错误', { icon: 2, time: 1000 });
                return;
            }

            var avatarFile = null;
            var barcodeatureFile = null;

            // 头像上传组件
            upload.render({
                elem: '#avatar_file',
                auto: false,
                accept: 'file',
                choose: function (obj) {
                    obj.preview(function (index, file) {
                        avatarFile = file;
                        $('#avatar_img').attr('src', URL.createObjectURL(file));
                    });
                },
            });

            // 签名上传组件
            upload.render({
                elem: '#barcode_file',
                auto: false,
                accept: 'file',
                choose: function (obj) {
                    obj.preview(function (index, file) {
                        barcodeatureFile = file;
                        $('#barcode_img').attr('src', URL.createObjectURL(file));
                    });
                },
            });

            // 请求科室/部门数据
            function fetchDepartmentData() {
                $.ajax({
                    url: '/normal/department_cache_get',
                    type: 'post',
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            let data = res.data;
                            let department = data.find(item => item.Id === department_id);
                            if (department) {
                                let departmentName = department.Name;
                                let departmentValue = `
                                <i class='iconfont'>&#xe686;</i>
                                <div>${departmentName}</div>
                            `;
                                $('#Department').html(departmentValue);
                            }
                            let treeData = format_to_treedata_department(data);
                            treeData = renderDropdownItems(treeData);
                            dropdown.render({
                                elem: '#Department',
                                id: 'DropdownID',
                                data: [],
                                content: '<div class="dropdown-menu">' + treeData + '</div>',
                                ready: function (elemPanel, elem) {
                                    elemPanel.on('click', '.dropdown-item-leaf', function () {
                                        var text = $(this).text();
                                        var value = `
                                            <i class='iconfont'>&#xe686;</i>
                                            <div>${text}</div>
                                        `;
                                        department_id = $(this).data('value');
                                        $('#Department').html(value);
                                        dropdown.close('DropdownID');
                                    });
                                }
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    },
                });
            }

            // 请求用户数据并填充表单
            function fetchUserData() {
                layer.load(2);
                $.ajax({
                    url: '/admin/user/list',
                    type: 'post',
                    data: { id: id },
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            var data = res.data[0];
                            form.val('form_edit', {
                                phone: data.Phone,
                                name: data.Name,
                                sort: data.Sort,
                                status: data.Status,
                            });
                            department_id = data.Department_id;
                            fetchDepartmentData();

                            // 加载头像
                            let avatarUrl = "/static/uploads/icons/avatarurl_" + id + ".png";
                            let avatarImg = new Image();
                            avatarImg.src = avatarUrl;
                            avatarImg.onload = function () {
                                $('#avatar_img').attr('src', avatarUrl + "?" + Math.random());
                            }

                            // 加载签名
                            let barcodeUrl = "/static/uploads/icons/barcode_" + id + ".png";
                            let barcodeImg = new Image();
                            barcodeImg.src = barcodeUrl;
                            barcodeImg.onload = function () {
                                $('#barcode_img').attr('src', barcodeUrl + "?" + Math.random());
                            }
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    },
                });
            }

            // 初始化科室数据和用户数据
            fetchUserData();

            // 图片预览功能
            $('#avatar_img, #barcode_img').on('click', function () {
                var imgId = this.id === 'avatar_img' ? 'avatar_img' : 'barcode_img';
                var imgSrc = $('#' + imgId).attr('src');
                if (imgSrc) {
                    layer.open({
                        type: 1,
                        title: false,
                        closeBtn: 1,
                        shadeClose: true,
                        area: ['500px', 'auto'],
                        content: '<div style="text-align: center;"><img src="' + imgSrc + '" style="max-width: 100%; max-height: 100%;"></div>'
                    });
                }
            });

            // 表单提交
            form.on('submit(formSubmitBtn)', function (data) {
                layer.load(2);
                var formData = new FormData();

                if (avatarFile) {
                    formData.append('avatar_file', avatarFile);
                }
                if (barcodeatureFile) {
                    formData.append('barcode_file', barcodeatureFile);
                }

                for (var key in data.field) {
                    if (data.field.hasOwnProperty(key)) {
                        formData.append(key, data.field[key]);
                    }
                }
                formData.append('id', id);
                formData.append('department_id', department_id);

                $.ajax({
                    url: '/admin/user/edit',
                    type: 'post',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (res) {
                        layer.closeAll('loading');
                        if (res.code === 200) {
                            layer.msg(res.msg, { icon: 1, time: 1000 }, function () {
                                parent.layer.close(index);
                                parent.layui.table.reload('user_list');
                            });
                        } else {
                            layer.msg(res.msg, { icon: 2, time: 1000 });
                        }
                    },
                    error: function (err) {
                        layer.closeAll('loading');
                        layer.msg(err.responseJSON ? err.responseJSON.msg : '请求失败', { icon: 2, time: 1000 });
                    },
                });

                return false;
            });
        });
    </script>
</body>

</html>