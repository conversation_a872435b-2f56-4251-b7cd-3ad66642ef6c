const ENV_CONFIG = {
  LOCAL: 'https://video1.mushengtang.com:8088',
  PROD: 'https://workspace.mushengtang.com',
  DEV: 'https://dev.mushengtang.com',
};
const getServerUrl = () => {
  const hostname = window.location.hostname;
  if (hostname.toLowerCase() === 'video1.mushengtang.com'.toLowerCase()) {
    return ENV_CONFIG.LOCAL;
  } else if (hostname.toLowerCase() === 'dev.mushengtang.com'.toLowerCase()) {
    return ENV_CONFIG.DEV;
  } else {
    return ENV_CONFIG.PROD;
  }
};
const Comany_Names = ["幸年堂", "慕生堂", "永幸堂"];
const serverUrl = getServerUrl();
// console.log('serverUrl:', serverUrl);
const FamilyRelation = [
  "本人",
  "父亲",
  "母亲",
  "配偶",
  "爷爷",
  "奶奶",
  "外祖父",
  "外祖母",
  "儿子",
  "女儿",
  "兄弟",
  "姐妹",
  "孙子",
  "孙女",
  "外孙",
  "外孙女",
  "叔叔",
  "阿姨",
  "侄子",
  "侄女",
  "表亲"
];
const Ins_Type = [
  "城镇职工基本养老保险",
  "城乡居民基本养老保险",
  "失业保险",
  "工伤保险",
  "生育保险",
  "城镇职工基本医疗保险",
  "城乡居民基本医疗保险",
  "新型农村合作医疗",
];
const Pay_review_status = [
  "订金待审",
  "已付订金",
  "尾款待付",
  "尾款待审",
  "已付尾款",
];
const Drug_unit = [
  "付",
  "袋",
  "瓶",
  "克",
];
const Drug_Usage = [
  "口服",
  "冲服",
  "含漱",
  "涂抹",
  "敷",
  "贴",
  "水煎服",
]

const Patient_From = [
  "转介绍",
  "员工介绍",
  "互联网",
  "线下",
]


// 病历状态
const Record_Status = [
  "待建诊室",
  "待问诊",
  "待下单",
  "无效病历(已废弃)",
  "待开方",
  "已开方",
  "病历完结"
];

// 问诊状态
const Room_Status = [
  "待诊",
  "已诊",
  "已取消",
  "已过期",
];

// 订单状态
const Order_Status = [
  "待发",
  "可发",
  "已发",
  "已收",
  "已退",
  "异常",
  "待转售后",
  "待分配",
  "已核销",
];

const order_status_list = [
  {
    name: "待发",
    icon: "&#xe63e;",
    color: "#FFB020"
  },
  {
    name: "可发",
    icon: "&#xe64d;",
    color: "#4CAF50"
  },
  {
    name: "已发",
    icon: "&#xe6e1;",
    color: "#2196F3"
  },
  {
    name: "已收",
    icon: "&#xe646;",
    color: "#4CAF50"
  },
  {
    name: "已退",
    icon: "&#xe6ee;",
    color: "#FF5722"
  },
  {
    name: "异常",
    icon: "&#xe648;",
    color: "#F44336"
  },
  {
    name: "待转售后",
    icon: "&#xe668;",
    color: "#b49a95"
  }, {
    name: "待分配",
    icon: "&#xe63e;",
    color: "#b49a95"
  },
  {
    name: "已核销",
    icon: "&#xe646;",
    color: "#4CAF50"
  }
];

// 处方状态
const Pre_Status = [
  "状态占位符",
  "已开方，待审核",
  "已驳回",
  "签章过半待调剂",
  "已调剂",
  "已入成品库",
  "已出库发药",
  "已异常废弃",
]

// 四角色通用审核状态
const foru_role_verify_status = [
  "未审核",
  "已审核",
  "审未过"
]

// 物流公司
const KD100_Express_Info = [
  {
    code: "shunfeng",
    name: "顺丰速运"
  },
  {
    code: "shentong",
    name: "申通快递"
  },
  {
    code: "yuantong",
    name: "圆通速递"
  },
  {
    code: "zhongtong",
    name: "中通快递"
  },
  {
    code: "yunda",
    name: "韵达快递"
  },
  {
    code: "jd",
    name: "京东物流"
  },
  {
    code: "sut56",
    name: "速通快递"
  },
  {
    code: "debangwuliu",
    name: "德邦快递"
  },
  {
    code: "ems",
    name: "中国邮政速递"
  },
  {
    code: "jtexpress",
    name: "极兔速递"
  },
  {
    code: "zhaijisong",
    name: "宅急送"
  },
  {
    code: "fedex",
    name: "FedEx联邦快递"
  },
  {
    code: "ups",
    name: "UPS联合包裹"
  }
];

var global_article_pic_path = '/static/uploads/normal_pics/article/';

// 全局-门店ID
var global_default_store_id = 26;
var global_department_expandIds = [26, 20, 21, 23]; // 部门 (科室) 列表，销售类默认展开的ID
var global_sale_data_master = 60;//触发销售数据查看AJAX条件的角色ID
var global_sale_department_pre_sale = 20; // 售前部门ID
var global_sale_department_after_sale = 21; // 售后部门ID
var global_sale_department_ids = [global_sale_department_pre_sale, global_sale_department_after_sale];//销售岗部门IDS
var global_doctor_role_id = 4; // 医生角色ID
var global_asst_role_id = 3;//售前医助角色ID
var global_after_asst_role_id = 9;//售后医助角色ID
var global_sale_pre_clerk_role_id = 6;//售前文员角色ID
var global_sale_after_clerk_role_id = 14;//售后文员角色ID

//需要有内置科室，如医生的科室、售前、售后、仓储，这些为系统内置科室的科室名，因为流程中需要调用这些科室下的人员
// 根据门店ID与内置科室名字，查找该科室下的人员信息
var get_userlist_by_sys_department_name_and_storeid = function (global_default_store_id, department_name) {

}

//static/uploads/icons/department_1.svg
function is_mini() {
  return window.innerWidth < 768 ? true : false;
}
function isEmpty(obj) {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
}
function isPhone(input) {
  input.value = input.value.replace(/\D/gi, '');
  if (input.value.length > 11 || input.value[0] !== '1') {
    input.value = input.value.substring(0, 11);
    if (input.value[0] !== '1') {
      input.value = '';
    }
  }
}
function removeTrailingZeroes(number) {
  if (Number.isInteger(number)) {
    return number;
  } else {
    let numStr = number.toString();
    let decimalIndex = numStr.indexOf('.');
    if (decimalIndex !== -1 && decimalIndex < numStr.length - 1) {
      let trimmedNum = numStr.replace(/0+$/, '');
      if (trimmedNum.charAt(trimmedNum.length - 1) === '.') {
        return trimmedNum.slice(0, -1);
      } else {
        return trimmedNum;
      }
    } else {
      return number;
    }
  }
}
function containsChinese(str) {
  var regex = /[\u4e00-\u9fa5]/;
  return regex.test(str);
}

// 将无限分组科室数据转换为树形结构数据，不完善，只排序了底层阶段
// var format_to_treedata_department = function (data) {
//   let treeData = [];
//   let mappedArr = {};
//   data.forEach(item => {
//     mappedArr[item.Id] = { ...item, children: [] };
//   });
//   for (let Id in mappedArr) {
//     let item = mappedArr[Id];
//     if (item.Pid === 0) {
//       treeData.push(item);
//     } else {
//       if (mappedArr[item.Pid]) {
//         mappedArr[item.Pid].children.push(item);
//       }
//     }
//   }
//   function sortChildren(node) {
//     if (node.children && node.children.length > 0) {
//       node.children.sort((a, b) => b.Sort - a.Sort);
//       node.children.forEach(child => sortChildren(child));
//     }
//   }
//   treeData.forEach(root => sortChildren(root));
//   return treeData;
// }
// 将无限分组科室数据转换为树形结构数据，完善，排序了所有节点
// var format_to_treedata_department = function (data) {
//   let treeData = [];
//   let mappedArr = {};
//   data.forEach(item => {
//     mappedArr[item.Id] = { ...item, children: [] };
//   });

//   for (let Id in mappedArr) {
//     let item = mappedArr[Id];
//     if (item.Pid === 0) {
//       treeData.push(item);
//     } else {
//       if (mappedArr[item.Pid]) {
//         mappedArr[item.Pid].children.push(item);
//       }
//     }
//   }

//   function sortChildren(node) {
//     if (node.children && node.children.length > 0) {
//       node.children.sort((a, b) => b.Sort - a.Sort);
//       node.children.forEach(child => sortChildren(child));
//     }
//   }

//   // 对根节点进行排序
//   treeData.sort((a, b) => b.Sort - a.Sort);
//   treeData.forEach(root => sortChildren(root));

//   return treeData;
// }

var format_to_treedata_department = function (data, rootId = null) {
  let treeData = [];
  let mappedArr = {};
  data.forEach(item => {
    mappedArr[item.Id] = { ...item, children: [] };
  });

  for (let Id in mappedArr) {
    let item = mappedArr[Id];
    if (item.Pid === 0) {
      treeData.push(item);
    } else {
      if (mappedArr[item.Pid]) {
        mappedArr[item.Pid].children.push(item);
      }
    }
  }

  function sortChildren(node) {
    if (node.children && node.children.length > 0) {
      node.children.sort((a, b) => b.Sort - a.Sort);
      node.children.forEach(child => sortChildren(child));
    }
  }

  // 如果指定了rootId，则只保留rootId的子节点
  if (rootId) {
    let rootNode = mappedArr[rootId];
    if (rootNode && rootNode.children) {
      sortChildren(rootNode);
      return rootNode.children; // 只返回子节点
    } else {
      return []; // 如果指定的rootId不存在或没有子节点，则返回空数组
    }
  } else {
    // 对根节点进行排序
    treeData.sort((a, b) => b.Sort - a.Sort);
    treeData.forEach(root => sortChildren(root));
    return treeData;
  }
}

// function renderDropdownItems(data) {
//   // dropdown - 渲染部门/科室分组，三级分类
//   var html = '';
//   data.forEach(function (group) {
//     html += '<div class="dropdown-item-group">' + group.Name + '</div>';
//     if (group.children && group.children.length > 0) {
//       group.children.forEach(function (subgroup) {
//         html += '<div class="dropdown-item-subgroup">' + subgroup.Name + '</div>';
//         if (subgroup.children && subgroup.children.length > 0) {
//           subgroup.children.forEach(function (leaf) {
//             html += '<div class="dropdown-item-leaf" data-value="' + leaf.Id + '">' + leaf.Name + '</div>';
//           });
//         }
//       });
//     }
//   });
//   return html;
// }
function renderDropdownItems_2(data) {
  var html = '';
  data.forEach(function (group) {
    html += '<div class="dropdown-item-group" data-value="' + group.Id + '">' + group.Name + '</div>';
    if (group.children && group.children.length > 0) {
      group.children.forEach(function (subgroup) {
        html += '<div class="dropdown-item-subgroup" data-value="' + subgroup.Id + '">' + subgroup.Name + '</div>';
      });
    }
  });
  return html;
}
function renderDropdownItems(data, level = 0) {
  var html = '';
  data.forEach(function (item) {
    // 根据层级动态设置类名和样式
    var className = item.children && item.children.length > 0
      ? 'dropdown-item-group' // 有子节点的元素
      : 'dropdown-item-leaf'; // 没有子节点的元素
    var paddingLeft = level * 20 + 10; // 每级缩进 20px

    html += '<div class="' + className + '" style="padding-left: ' + paddingLeft + 'px" data-value="' + (item.Id || '') + '">'
      + item.Name +
      '</div>';
    // 如果有子节点，递归渲染
    if (item.children && item.children.length > 0) {
      html += renderDropdownItems(item.children, level + 1);
    }
  });
  return html;
}

var format_to_treedata_role_res = function (data) {
  // TREETABLE - 将权限API返回的树形数据转换为适合格式的数据
  let treeData = [];
  if (data) {
    let mappedArr = {};
    data.sort((a, b) => b.sort - a.sort);
    data.forEach(item => {
      mappedArr[item.id] = { ...item, children: [] }; // 添加 children 属性
    });
    for (let id in mappedArr) {
      let item = mappedArr[id];
      if (item.pid === 0) {
        treeData.push(item);
      } else {
        if (mappedArr[item.pid]) {
          mappedArr[item.pid].children.push(item);
        }
      }
    }
    treeData.sort((a, b) => b.sort - a.sort);
    function sortChildren(node) {
      if (node.children && node.children.length > 0) {
        node.children.sort((a, b) => b.sort - a.sort);
        node.children.forEach(child => sortChildren(child));
      }
    }
    treeData.forEach(root => sortChildren(root));
  }
  return treeData;
}

function processAndStorePermResData(data) {
  // 把当前用户权限写入本地存储
  if (data) {
    const processedData = data.map(item => ({
      id: item.id,
      pid: item.pid,
      name: item.name,
      sort: item.sort,
      type: item.type,
      status: item.status
    }));
    // 将处理后的数据存储到 localStorage
    localStorage.setItem('local_perm_res_data', JSON.stringify(processedData));
  }
}

function processAndStoreMenuData(formattedData) {
  // 把当前用户菜单写入本地存储
  const filteredData = [];
  function processMenuItem(item) {
    const processedItem = {
      id: item.id,
      menu_icon: item.menu_icon,
      name: item.name,
      pid: item.pid,
      url: item.url,
      children: []
    };
    // 递归处理子菜单
    item.children.forEach(child => {
      if (child.status !== 0 && child.type === 0) { // 过滤无效子菜单项
        processedItem.children.push(processMenuItem(child));
      }
    });
    return processedItem;
  }
  formattedData.forEach(item => {
    if (item.status !== 0 && item.type === 0) { // 过滤无效菜单项
      filteredData.push(processMenuItem(item));
    }
  });
  // 存储到 localStorage
  localStorage.setItem('local_menu_data', JSON.stringify(filteredData));
}

function render_menu($) {
  let local_menu_data = JSON.parse(localStorage.getItem('local_menu_data'));
  if (!local_menu_data || local_menu_data.length === 0) {
    layer.msg('未找到菜单数据，请联系管理员确认权限分配情况。');
    return;
  }
  function generateHTML(menuTree) {
    let html = '';
    menuTree.forEach(item => {
      if (item.children && item.children.length > 0) {
        // 有子菜单，生成嵌套结构
        html += `
          <li class="layui-nav-item">
            <a href="javascript:;"><i class='iconfont'>${item.menu_icon}</i><cite>${item.name}<cite></a>
            <dl class="layui-nav-child">
              ${generateChildHTML(item.children)}
            </dl>
          </li>
        `;
      } else {
        // 没有子菜单，直接生成菜单项
        html += `
          <li class="layui-nav-item">
            <dd><a href="${item.url}"><i class='iconfont'>${item.menu_icon}</i>${item.name}</a></dd>
          </li>
        `;
      }
    });
    return html;
  }


  // 生成子菜单的 HTML
  function generateChildHTML(children) {
    let html = '';
    children.forEach(child => {
      html += `
        <dd>
          <a href="${child.url}">
            <i class='iconfont'>${child.menu_icon == 0 ? '' : child.menu_icon}</i>
            <cite>${child.name}</cite>
          </a>
        </dd>
      `;
    });
    return html;
  }
  $('#menuContainer').html(generateHTML(local_menu_data));
  const element = layui.element;
  element.render('nav');//重新渲染layui菜单

  var menuStateKey = 'menu_open_state';
  var menuState = localStorage.getItem(menuStateKey);
  $('#menuContainer').find('.layui-nav-item').each(function (index) {
    if (menuState && Number(menuState) === index) {
      $(this).addClass('layui-nav-itemed');
    }
  });
  var pathname = window.location.href;
  pathname_arr = pathname.split('/');
  let rightpath = "";
  for (let i = 0; i < pathname_arr.length; i++) {
    if (i > 2) {
      rightpath += "/" + pathname_arr[i];
    }
  }
  if (rightpath.indexOf('#') > -1) {
    rightpath = rightpath.split('#')[1];
  }
  // console.log(rightpath);
  $('#menuContainer').find('a').each(function () {
    if ($(this).attr('href') === rightpath) {
      $(this).parent().addClass('layui-this');
      var $parentItem = $(this).parents('.layui-nav-item');
      $parentItem.addClass('layui-nav-itemed');
      var parentIndex = $('#menuContainer .layui-nav-item').index($parentItem);
      localStorage.setItem(menuStateKey, parentIndex.toString());
      return false;
    }
  });

  $('#menuContainer').on('click', '.layui-nav-item > a', function () {
    var $parent = $(this).parent();
    var index = $('#menuContainer .layui-nav-item').index($parent);
    if ($parent.hasClass('layui-nav-itemed')) {
      localStorage.setItem(menuStateKey, index.toString());
    } else {
      localStorage.removeItem(menuStateKey);
    }
  });

}

// 根据权限IDS转权限名字
function perm_id2name(data, ids) {
  let result = [];
  // 将 ids 字符串拆分为数组，并转换为数字
  const idArray = ids.split(',').map(id => Number(id.trim()));
  data.forEach(item => {
    if (idArray.includes(item.id)) {
      // console.log(ids + ' 包含 ' + item.id + ' => ' + item.name);
      result.push(item.name);
    }
  });

  return result;
}
// 获取选择的权限IDS
function extractIds(data) {
  let ids = [];
  function recursiveExtract(children) {
    children.forEach(child => {
      if (child.children && child.children.length > 0) {
        // 如果当前节点有子节点，则递归处理子节点，不添加当前节点的ID
        recursiveExtract(child.children);
      } else {
        // 如果当前节点没有子节点，则添加其ID
        ids.push(child.id);
      }
    });
  }
  // 调用递归函数
  recursiveExtract(data);
  return ids;
}

// id转科室名
var id2department = function (id, departments, type = 1) {
  function findPath(id, departments) {
    let department = departments.find(item => item.Id === id);
    if (!department) {
      return null;
    }
    let path = department.Name;
    if (department.Pid !== 0) {
      let parentPath = findPath(department.Pid, departments);
      if (parentPath) {
        path = `${parentPath} - ${path}`;
      }
    }
    return path;
  }
  if (type === 0) {
    return departments.find(item => item.Id === id)?.Name || '-';
  } else if (type === 1) {
    return findPath(id, departments) || '-';
  }
  return '-';
}
// 获取URL参数
const request = {
  get: (name) => {
    return new URLSearchParams(window.location.search).get(name);
  }
};
// 检查页面按钮权限并禁用不具备权限的按钮
function render_button($) {
  const local_perm_res_data = JSON.parse(localStorage.getItem('local_perm_res_data')) || [];
  const permissionIds = new Set(local_perm_res_data.map(item => item.id));
  $('.perm_check_btn').each(function () {
    const resId = Number($(this).attr('res_id'));
    if (!permissionIds.has(resId)) {
      $(this).hide();
    }
  });
}
//刷新缓存
function refresh_cache($) {
  layer.load(2);
  // 读取本地存储的local_userinfo
  const local_userinfo = JSON.parse(localStorage.getItem('local_userinfo'));
  let url = local_userinfo.Id == 1 ? '/admin/res/current' : '/admin/res/current_refresh';
  $.ajax({
    url: url,
    type: 'post',
    dataType: 'json',
    success: function (res) {
      layer.closeAll('loading');
      // 更新时遇到当前用户为销售管理员时
      let dep_ids = res.dep_ids || "";
      if (dep_ids) {
        localStorage.setItem("local_userinfo", JSON.stringify({
          ...JSON.parse(localStorage.getItem("local_userinfo") || "{}"),
          Dep_ids: dep_ids
        }));
      }
      // 将当前部门数据缓存本地
      let local_departments = res.departments || [];
      if (local_departments.length > 0) {
        localStorage.setItem("local_departments", JSON.stringify(local_departments));
      }
      //销售管理员逻辑结束
      let data = res.data;
      // 存储所有数据 - local_perm_res_data
      processAndStorePermResData(data);
      let formattedData = format_to_treedata_role_res(data);
      // 存储菜单格式数据 - local_menu_data
      processAndStoreMenuData(formattedData);
      layer.msg('缓存刷新成功！');
      setTimeout(() => {
        window.location.reload();
      }, 300);
    },
    error: function (data) {
      layer.closeAll('loading');
      layer.msg(data.responseJSON.msg);
    }
  });
}
// 根据日期转年龄
function date2age(dateStr) {
  const birthDate = new Date(dateStr);
  const ageDifMs = Date.now() - birthDate.getTime();
  const ageDate = new Date(ageDifMs); // miliseconds from epoch
  return Math.abs(ageDate.getUTCFullYear() - 1970);
}
// 获取今天日期YYYY-MM-DD
function getToday() {
  const date = new Date();
  const separator = "-";
  const year = date.getFullYear();
  let month = date.getMonth() + 1; // 用let声明，方便后续修改
  let strDate = date.getDate(); // 用let声明，方便后续修改
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 1 && strDate <= 9) { // 应该是1到9，因为日期不可能为0
    strDate = "0" + strDate;
  }
  return year + separator + month + separator + strDate;
}

// 获取明天日期YYYY-MM-DD
function getTomorrow() {
  const date = new Date();
  date.setDate(date.getDate() + 1);
  const separator = "-";
  const year = date.getFullYear();
  let month = date.getMonth() + 1; // 用let声明，方便后续修改
  let strDate = date.getDate(); // 用let声明，方便后续修改
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 1 && strDate <= 9) { // 应该是1到9，因为日期不可能为0
    strDate = "0" + strDate;
  }
  return year + separator + month + separator + strDate;
}
// 返回本月的第1天和最后1天，格式为YYYY-MM-DD
function getFirstAndLastDayOfMonth() {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth();
  const firstDay = new Date(year, month, 1);
  const firstDayStr = firstDay.toLocaleDateString('en-CA');
  const lastDay = new Date(year, month + 1, 0);
  const lastDayStr = lastDay.toLocaleDateString('en-CA');
  return {
    firstDay: firstDayStr,
    lastDay: lastDayStr
  };
}
//关闭LAYUI弹出来的WINDOW
function closeModalWindow() {
  var index = parent.layer.getFrameIndex(window.name);
  parent.layer.close(index);
}

// 添加可拖动提示框功能
function initDraggableDelPm(selector = '.del_pm') {
  var delPm = document.querySelector(selector);
  if (!delPm) return;

  // 防止重复初始化
  if (delPm.getAttribute('data-draggable-initialized')) return;
  delPm.setAttribute('data-draggable-initialized', 'true');

  var isDragging = false;
  var currentY;
  var initialY;
  var yOffset = 0;

  function dragStart(e) {
    // 检查点击的元素是否是链接或者链接的子元素
    let targetElement = e.target;
    let isLink = false;
    while (targetElement && targetElement !== delPm) {
      if (targetElement.tagName.toLowerCase() === 'a') {
        isLink = true;
        break;
      }
      targetElement = targetElement.parentElement;
    }

    // 如果点击的不是链接，且是在del_pm内部，则允许拖动
    if (!isLink && delPm.contains(e.target)) {
      initialY = e.clientY - yOffset;
      isDragging = true;
      // 添加禁止选择文本的类
      document.body.classList.add('no-select');
    }
  }

  function drag(e) {
    if (isDragging) {
      e.preventDefault();
      currentY = e.clientY - initialY;

      // 限制拖动范围，防止拖出屏幕
      if (currentY < -window.innerHeight * 0.15) {
        currentY = -window.innerHeight * 0.15;
      }
      if (currentY > window.innerHeight * 0.85) {
        currentY = window.innerHeight * 0.85;
      }

      yOffset = currentY;
      delPm.style.transform = "translateY(" + currentY + "px)";
    }
  }

  function dragEnd(e) {
    initialY = currentY;
    isDragging = false;
    // 移除禁止选择文本的类
    document.body.classList.remove('no-select');
  }

  delPm.addEventListener("mousedown", dragStart);
  document.addEventListener("mousemove", drag);
  document.addEventListener("mouseup", dragEnd);
}

// 自动初始化拖动功能
(function () {
  // 创建一个 MutationObserver 实例
  var observer = new MutationObserver(function (mutations) {
    mutations.forEach(function (mutation) {
      if (mutation.target.classList.contains('del_pm')) {
        // 如果 del_pm 元素变为显示状态，初始化拖动功能
        if (mutation.target.style.display === 'block') {
          initDraggableDelPm();
        }
      }
    });
  });

  // 当 DOM 加载完成后开始观察
  document.addEventListener('DOMContentLoaded', function () {
    // 观察 .del_pm 元素的显示状态变化
    var delPm = document.querySelector('.del_pm');
    if (delPm) {
      observer.observe(delPm, {
        attributes: true,
        attributeFilter: ['style']
      });

      // 如果页面加载时就是显示状态，也初始化
      if (window.getComputedStyle(delPm).display !== 'none') {
        initDraggableDelPm();
      }
    }
  });
})();

// 图片压缩函数
function compressImage(file, maxWidth = 600, quality = 0.9) {
  return new Promise((resolve, reject) => {
    // 如果文件不是图片，直接返回原文件
    if (!file.type.startsWith('image/')) {
      resolve(file);
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (e) {
      const img = new Image();
      img.src = e.target.result;
      img.onload = function () {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // 计算新的尺寸
        let width = img.width;
        let height = img.height;

        if (width > maxWidth) {
          height = Math.round((maxWidth * height) / width);
          width = maxWidth;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制图片
        ctx.drawImage(img, 0, 0, width, height);

        // 转换为blob
        canvas.toBlob(function (blob) {
          resolve(blob);
        }, file.type, quality);
      };
      img.onerror = function () {
        reject(new Error('图片加载失败'));
      };
    };
    reader.onerror = function () {
      reject(new Error('文件读取失败'));
    };
  });
}

// 图片自动压缩上传函数
function autoCompressAndUpload(file, options = {}, $ = layui.$) {
  const defaultOptions = {
    url: '/admin/upload_normal_pic',  // 上传地址
    maxSize: 1024 * 1024,            // 最大文件大小（1MB）
    maxWidth: 600,                    // 最大宽度
    quality: 0.9,                     // 压缩质量
    data: {},                         // 额外的表单数据
    success: null,                    // 成功回调
    error: null,                      // 错误回调
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp']  // 允许的文件类型
  };

  // 合并选项
  const finalOptions = { ...defaultOptions, ...options };

  // 检查文件类型
  if (!finalOptions.allowedTypes.includes(file.type)) {
    if (finalOptions.error) {
      finalOptions.error(new Error('不支持的文件类型(' + file.type + ')'));
    }
    return;
  }

  // 显示上传中提示
  const loadingIndex = layer.load(2);

  // 如果文件大于限制大小，进行压缩
  if (file.size > finalOptions.maxSize) {
    compressImage(file, finalOptions.maxWidth, finalOptions.quality)
      .then(compressedBlob => {
        if (compressedBlob.size > finalOptions.maxSize) {
          layer.close(loadingIndex);
          if (finalOptions.error) {
            finalOptions.error(new Error('压缩后文件仍然超过大小限制（' + (compressedBlob.size / 1024 / 1024).toFixed(2) + 'M）'));
          }
          return;
        }
        uploadFile(compressedBlob);
      })
      .catch(error => {
        layer.close(loadingIndex);
        if (finalOptions.error) {
          finalOptions.error(error);
        }
      });
  } else {
    uploadFile(file);
  }

  function uploadFile(fileToUpload) {
    const formData = new FormData();
    formData.append('file', fileToUpload);

    // 添加额外的表单数据
    Object.keys(finalOptions.data).forEach(key => {
      formData.append(key, finalOptions.data[key]);
    });

    $.ajax({
      url: finalOptions.url,
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success: function (res) {
        layer.close(loadingIndex);
        if (finalOptions.success) {
          finalOptions.success(res);
        }
      },
      error: function (err) {
        layer.close(loadingIndex);
        if (finalOptions.error) {
          finalOptions.error(err);
        }
      }
    });
  }
}
function Utc2time(time) {
  return time.replace("T", " ").replace("Z", "");
}

// Cookies操作相关函数
function setCookie(name, value, days) {
  var expires = "";
  if (days) {
    var date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    expires = "; expires=" + date.toUTCString();
  }
  document.cookie = name + "=" + (value || "") + expires + "; path=/";
}

function getCookie(name) {
  var nameEQ = name + "=";
  var ca = document.cookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

function deleteCookie(name) {
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

function linuxTimestampToNormalTime(timestamp) {
  if (timestamp === '0') {
    return '/';
  }
  var date = new Date(timestamp * 1000);
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var hour = date.getHours();
  var minute = date.getMinutes();
  var second = date.getSeconds();
  return year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
}

function formatTimeAgo(timeStr) {
  if (!timeStr) return "-";

  // 创建日期对象
  const date = new Date(timeStr);
  // 如果时间格式不正确，直接返回原始时间字符串
  if (isNaN(date.getTime())) return timeStr;

  // 获取当前时间
  const now = new Date();
  // 计算时间差（毫秒）
  const diffMs = now - date;
  // 转换为分钟
  const diffMinutes = Math.floor(diffMs / (1000 * 60));

  // 根据时间差返回不同的文字和颜色
  if (diffMinutes <= 5) {
    // 5分钟内：显示"刚刚"，绿色
    return '<span style="color: #4CAF50;">刚刚</span>';
  } else if (diffMinutes <= 30) {
    // 1小时内：显示"1小时内"，80%绿
    return '<span style="color: rgba(76, 175, 80, 0.8);">半小时内</span>';
  } else if (diffMinutes <= 60) {
    // 1小时内：显示"1小时内"，80%绿
    return '<span style="color: rgba(76, 175, 80, 0.8);">1小时内</span>';
  } else if (diffMinutes <= 120) {
    // 2小时内：显示"2小时内"，60%绿
    return '<span style="color: rgba(76, 175, 80, 0.6);">2小时内</span>';
  } else if (diffMinutes <= 180) {
    // 3小时内：显示"3小时内"，50%绿
    return '<span style="color: rgba(76, 175, 80, 0.5);">3小时内</span>';
  } else if (diffMinutes <= 300) {
    // 5小时内：显示"5小时内"，30%绿
    return '<span style="color: rgba(76, 175, 80, 0.3);">5小时内</span>';
  } else {
    // 超过5小时：直接显示原始时间，黑色
    return '<span style="color: #000;">' + timeStr + '</span>';
  }
}
//去掉指定字符串中所有的|符号
function removeAllPipe(str) {
  return str.replace(/\|/g, ' ');
}

// 检查一个元素是否在数组中
function in_array(needle, haystack) {
  if (!Array.isArray(haystack)) {
    return false;
  }
  return haystack.indexOf(needle) !== -1;
}