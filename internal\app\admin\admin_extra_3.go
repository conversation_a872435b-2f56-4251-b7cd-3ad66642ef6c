package admin

import (
	"context"
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	cos "github.com/tencentyun/cos-go-sdk-v5"
)

// 线上诊室列表
func Rtc_room_list(w http.ResponseWriter, r *http.Request) {
	api_id := 85
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 3 {
		limit = 3
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit

	// 其它参数
	var params []any

	// ----------- 新增销售数据管理员后的药物筛选开始
	var attSql string
	var is_doc bool = false
	var is_asst bool = false
	if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
		//医助（售前、售前）
		is_asst = true
		attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "4" {
		//医生
		is_doc = true
		attSql += " AND doc_id =  " + strconv.Itoa(session.Values["id"].(int))
	}
	// 如果不是医生角色
	if !is_doc {
		doc_id := r.FormValue("doc_id")
		if doc_id != "" {
			attSql += " AND doc_id = " + doc_id
		} else {
			doc_dep_id := r.FormValue("doc_dep_id")
			if doc_dep_id != "" {
				attSql += " AND department_id = " + doc_dep_id
			}
		}
	}
	// 如果不是医助角色
	if !is_asst {
		asst_id := r.FormValue("asst_id")
		if asst_id != "" {
			attSql += " AND asst_id = " + asst_id
		} else {
			asst_dep_id := r.FormValue("asst_dep_id")
			if session.Values["role_ids"] == "2" {
				// 销售数据管理员
				if asst_dep_id != "" {
					// 判断前端传递的部门ID是否在dep_ids中
					asst_dep_ids := strings.Split(asst_dep_id, ",")
					dep_ids_arr := strings.Split(session.Values["dep_ids"].(string), ",")
					// 将dep_ids转换为map，便于快速查找
					dep_ids_map := make(map[string]bool)
					for _, id := range dep_ids_arr {
						dep_ids_map[id] = true
					}
					// 检查asst_dep_id中的每个元素是否都存在于dep_ids中
					for _, id := range asst_dep_ids {
						if !dep_ids_map[id] {
							common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
								"code": 500,
								"msg":  "您请求的部门中，有您无权查看的部门，这可能是您的权限已被调整 ( " + asst_dep_id + " vs " + session.Values["dep_ids"].(string) + " )",
							})
							return
						}
					}
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				} else {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + session.Values["dep_ids"].(string) + "))"
				}
			} else {
				// 其它角色，如医生、超管、文员等；不是医助也不是销售数据管理员
				if asst_dep_id != "" {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				}
			}
		}
	}
	// ----------- 新增销售数据管理员后的药物筛选结束

	// 患者ID
	pat_pro_id := r.FormValue("pat_pro_id")
	if pat_pro_id != "" {
		attSql += " AND pat_pro_id = ?"
		params = append(params, pat_pro_id)
	}
	// 病历ID
	record_id := r.FormValue("record_id")
	if record_id != "" {
		attSql += " AND record_id = ?"
		params = append(params, record_id)
	}
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM rtc_room WHERE 1 " + attSql
	err = database.GetOne(sql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	// 结构体
	type RtcRoom struct {
		ID             int    `db:"id"`             // 房间ID
		Department_id  int    `db:"department_id"`  // 科室ID
		Doc_id         int    `db:"doc_id"`         // 医生ID
		Asst_id        int    `db:"asst_id"`        // 医助ID
		Pat_id         int    `db:"pat_id"`         // 患者ID
		Pat_pro_id     int    `db:"pat_pro_id"`     // 患者档案ID
		Status         int    `db:"status"`         // 房间状态
		Scheduled_time string `db:"scheduled_time"` // 预约时间
		Create_time    string `db:"create_time"`    // 创建时间
		Record_id      int    `db:"record_id"`      // 病历ID
		Finish_time    string `db:"finish_time"`    // 完成时间
	}
	sql = "select id,department_id,doc_id,asst_id,record_id,pat_id,pat_pro_id,status,scheduled_time,create_time,finish_time from rtc_room where 1 " + attSql + " order by status asc, finish_time desc limit ? offset ?"
	params = append(params, limit, offset) // 将 limit 和 offset 添加到 params 切片中
	var rtc_rooms []RtcRoom
	err = database.GetAll(sql, &rtc_rooms, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "读取成功",
		"data":  rtc_rooms,
		"count": count,
		"sql":   common.DebugSql(sql, params...),
	})
}

// 患者列表使用，判断微信绑定用
func Patient_account_check_wxbind(w http.ResponseWriter, r *http.Request) {
	api_id := 45
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 带分页的查询
	attSql := ""
	ids := r.FormValue("ids")
	if ids == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "请选择要绑定的患者",
		})
		return
	}
	attSql += " AND id IN (" + ids + ")"
	// 数据查询
	type Patient_account struct {
		ID          int    `db:"id"`
		Phone       string `db:"phone"`
		Is_bind     int    `db:"is_bind"`
		Status      int    `db:"status"`
		Create_time string `db:"create_time"`
	}
	sql := "SELECT id,phone,case when openid = '0' then 0 else 1 end is_bind,status,create_time FROM patient_account WHERE 1 " + attSql
	var patient_account []Patient_account
	err := database.GetAll(sql, &patient_account)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "ok",
		"data": patient_account,
	})
}

// 患者帐号列表 45
func Patient_account_list(w http.ResponseWriter, r *http.Request) {
	api_id := 45
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// ----------- 新增销售数据管理员后的药物筛选开始
	var attSql string
	var is_asst bool = false
	if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
		//医助（售前、售前）
		is_asst = true
		attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	}
	// 如果不是医助角色
	if !is_asst {
		asst_id := r.FormValue("asst_id")
		if asst_id != "" {
			attSql += " AND asst_id = " + asst_id
		} else {
			asst_dep_id := r.FormValue("asst_dep_id")
			if session.Values["role_ids"] == "2" {
				// 销售数据管理员
				if asst_dep_id != "" {
					// 判断前端传递的部门ID是否在dep_ids中
					asst_dep_ids := strings.Split(asst_dep_id, ",")
					dep_ids_arr := strings.Split(session.Values["dep_ids"].(string), ",")
					// 将dep_ids转换为map，便于快速查找
					dep_ids_map := make(map[string]bool)
					for _, id := range dep_ids_arr {
						dep_ids_map[id] = true
					}
					// 检查asst_dep_id中的每个元素是否都存在于dep_ids中
					for _, id := range asst_dep_ids {
						if !dep_ids_map[id] {
							common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
								"code": 500,
								"msg":  "您请求的部门中，有您无权查看的部门，这可能是您的权限已被调整 ( " + asst_dep_id + " vs " + session.Values["dep_ids"].(string) + " )",
							})
							return
						}
					}
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				} else {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + session.Values["dep_ids"].(string) + "))"
				}
			} else {
				// 其它角色，如医生、超管、文员等；不是医助也不是销售数据管理员
				if asst_dep_id != "" {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				}
			}
		}
	}
	// ----------- 新增销售数据管理员后的药物筛选结束

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit

	// 如果ID不为空，直接ID =
	pat_id := r.FormValue("pat_id")
	if pat_id != "" {
		attSql += " AND id = " + pat_id
	}

	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM patient_account WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
		})
		return
	}
	// 数据查询
	type Patient_account struct {
		ID              int    `db:"id"`
		Asst_id         int    `db:"asst_id"`         // 医助ID
		Phone           string `db:"phone"`           //电话
		Is_bind         int    `db:"is_bind"`         //绑定状态
		Status          int    `db:"status"`          //帐号状态
		Is_transfer     int    `db:"is_transfer"`     // 是否已转售后
		Last_login_time string `db:"last_login_time"` //最后登录时间
		Last_login_ip   string `db:"last_login_ip"`   //最后登录IP
		Create_time     string `db:"create_time"`     //建立时间
	}
	sql = "SELECT id,phone,case when openid = '0' then 0 else 1 end is_bind,is_transfer,asst_id,status,last_login_time,last_login_ip,create_time FROM patient_account WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var patient_account []Patient_account
	err = database.GetAll(sql, &patient_account, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	// 处理手机号显示
	if session.Values["role_ids"] != "1" {
		// 遍历切片中的每个元素，处理手机号
		for i := range patient_account {
			if len(patient_account[i].Phone) == 11 || len(patient_account[i].Phone) == 13 {
				patient_account[i].Phone = patient_account[i].Phone[:5] + "****" + patient_account[i].Phone[len(patient_account[i].Phone)-2:]
			} else {
				patient_account[i].Phone = "手机号码格式错误"
			}
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  patient_account,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 患者列表 48
func Patient_profile_list(w http.ResponseWriter, r *http.Request) {
	api_id := 48
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// ----------- 新增销售数据管理员后的药物筛选开始
	var attSql string
	var is_doc bool = false
	var is_asst bool = false
	if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
		//医助（售前、售前）
		is_asst = true
		attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "4" {
		//医生
		is_doc = true
		attSql += " AND doc_id =  " + strconv.Itoa(session.Values["id"].(int))
	}
	// 如果不是医生角色
	if !is_doc {
		doc_id := r.FormValue("doc_id")
		if doc_id != "" {
			attSql += " AND doc_id = " + doc_id
		} else {
			// 当前患者表暂未设计单独部门字段，所以无法按部门筛选
			// doc_dep_id := r.FormValue("doc_dep_id")
			// if doc_dep_id != "" {
			// 	attSql += " AND department_id = " + doc_dep_id
			// }
		}
	}
	// 如果不是医助角色
	if !is_asst {
		asst_id := r.FormValue("asst_id")
		if asst_id != "" {
			attSql += " AND asst_id = " + asst_id
		} else {
			asst_dep_id := r.FormValue("asst_dep_id")
			if session.Values["role_ids"] == "2" {
				// 销售数据管理员
				if asst_dep_id != "" {
					// 判断前端传递的部门ID是否在dep_ids中
					asst_dep_ids := strings.Split(asst_dep_id, ",")
					dep_ids_arr := strings.Split(session.Values["dep_ids"].(string), ",")
					// 将dep_ids转换为map，便于快速查找
					dep_ids_map := make(map[string]bool)
					for _, id := range dep_ids_arr {
						dep_ids_map[id] = true
					}
					// 检查asst_dep_id中的每个元素是否都存在于dep_ids中
					for _, id := range asst_dep_ids {
						if !dep_ids_map[id] {
							common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
								"code": 500,
								"msg":  "您请求的部门中，有您无权查看的部门，这可能是您的权限已被调整 ( " + asst_dep_id + " vs " + session.Values["dep_ids"].(string) + " )",
							})
							return
						}
					}
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				} else {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + session.Values["dep_ids"].(string) + "))"
				}
			} else {
				// 其它角色，如医生、超管、文员等；不是医助也不是销售数据管理员
				if asst_dep_id != "" {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				}
			}
		}
	}
	// ----------- 新增销售数据管理员后的药物筛选结束
	// 用户ID
	pat_pro_id := r.FormValue("pat_pro_id")
	if pat_pro_id != "" {
		attSql += " AND id = " + pat_pro_id
	}
	// 帐号ID
	patient_account_id := r.FormValue("patient_account_id")
	if patient_account_id != "" {
		attSql += " AND pid = " + patient_account_id
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 3 {
		limit = 3
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM patient_profile WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
		})
		return
	}
	// 数据查询
	type Patient_profile struct {
		ID              int       `db:"id"`              // 主键
		Pid             int       `db:"pid"`             // 患者帐号id
		Asst_id         int       `db:"asst_id"`         // 销售id
		Doc_id          int       `db:"doc_id"`          // 医生id
		Support_id      int       `db:"support_id"`      // 支援人员id
		Relation        int       `db:"relation"`        // 与患者关系
		Phone           string    `db:"phone"`           // 手机号
		Name            string    `db:"name"`            // 姓名
		Sex             string    `db:"sex"`             // 性别
		Born_date       time.Time `db:"born_date"`       // 出生日期
		Idcard          string    `db:"idcard"`          // 身份证号
		Weixin          string    `db:"weixin"`          // 微信号
		Ins_card_num    string    `db:"ins_card_num"`    // 医保卡号
		Ins_type        string    `db:"ins_type"`        // 医保类型
		Height          float64   `db:"height"`          // 身高
		Weight          float64   `db:"weight"`          // 体重
		Allergies       string    `db:"allergies"`       // 过敏史
		Medical_history string    `db:"medical_history"` // 既往病史
		Patient_Type    int       `db:"Patient_Type"`    // 患者类型
		Patient_From    int       `db:"Patient_From"`    // 信息来源
		Address         string    `db:"address"`         // 地址
		Ismarried       int       `db:"Ismarried"`       // 是否已婚
		Status          int       `db:"status"`          // 状态
		Customer_notes  string    `db:"customer_notes"`  // 客户备注
		Last_time       string    `db:"last_time"`       // 最后跟进时间
		Level           string    `db:"level"`           // 级别
		Create_time     string    `db:"create_time"`     // 创建时间
	}
	sql = "SELECT id,pid,asst_id,doc_id,support_id,relation,phone,name,sex,born_date,idcard,weixin,ins_card_num,ins_type,height,weight,allergies,medical_history,Patient_Type,Patient_From,address,Ismarried,status,customer_notes,IFNULL(last_time,'未跟进')last_time,create_time,level FROM patient_profile WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var patient_profile []Patient_profile
	err = database.GetAll(sql, &patient_profile, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  patient_profile,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 订单列表 41
func Order_list(w http.ResponseWriter, r *http.Request) {
	api_id := 41
	// 订单表：orders
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// ----------- 新增销售数据管理员后的药物筛选开始
	var attSql string
	var is_doc bool = false
	var is_asst bool = false
	if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
		//医助（售前、售前）
		is_asst = true
		attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "4" {
		//医生
		is_doc = true
		attSql += " AND doc_id =  " + strconv.Itoa(session.Values["id"].(int))
	}

	// 如果不是医生角色
	if !is_doc {
		doc_id := r.FormValue("doc_id")
		if doc_id != "" {
			attSql += " AND doc_id = " + doc_id
		} else {
			doc_dep_id := r.FormValue("doc_dep_id")
			if doc_dep_id != "" {
				attSql += " AND doc_dep_id = " + doc_dep_id
			}
		}
	}
	// 如果不是医助角色
	if !is_asst {
		asst_id := r.FormValue("asst_id")
		if asst_id != "" {
			attSql += " AND asst_id = " + asst_id
		} else {
			asst_dep_id := r.FormValue("asst_dep_id")
			if session.Values["role_ids"] == "2" {
				// 销售数据管理员
				if asst_dep_id != "" {
					// 判断前端传递的部门ID是否在dep_ids中
					asst_dep_ids := strings.Split(asst_dep_id, ",")
					dep_ids_arr := strings.Split(session.Values["dep_ids"].(string), ",")
					// 将dep_ids转换为map，便于快速查找
					dep_ids_map := make(map[string]bool)
					for _, id := range dep_ids_arr {
						dep_ids_map[id] = true
					}
					// 检查asst_dep_id中的每个元素是否都存在于dep_ids中
					for _, id := range asst_dep_ids {
						if !dep_ids_map[id] {
							common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
								"code": 500,
								"msg":  "您请求的部门中，有您无权查看的部门，这可能是您的权限已被调整 ( " + asst_dep_id + " vs " + session.Values["dep_ids"].(string) + " )",
							})
							return
						}
					}
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				} else {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + session.Values["dep_ids"].(string) + "))"
				}
			} else {
				// 其它角色，如医生、超管、文员等；不是医助也不是销售数据管理员
				if asst_dep_id != "" {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
				}
			}
		}
	}
	// ----------- 新增销售数据管理员后的药物筛选结束

	status := r.FormValue("status")
	if status != "" {
		attSql += " AND status = " + status
	}
	// 患者ID
	pat_pro_id := r.FormValue("pat_pro_id")
	if pat_pro_id != "" {
		attSql += " AND pat_pro_id = " + pat_pro_id
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM orders WHERE 1 " + attSql
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
			"sql":  common.DebugSql(sql),
		})
		return
	}
	// 数据查询
	type Order struct {
		ID                int     `db:"id"`                // 主键ID
		Doc_id            int     `db:"doc_id"`            // 医生ID
		Asst_id           int     `db:"asst_id"`           // 医助ID（售前）
		Pat_id            int     `db:"pat_id"`            // 患者帐号ID
		Pat_pro_id        int     `db:"pat_pro_id"`        // 患者信息ID
		Total_money       float64 `db:"total_money"`       // 总款
		Pre_pay           float64 `db:"pre_pay"`           // 预付
		Record_ids        string  `db:"record_ids"`        // 病历ID集合
		Status            int     `db:"status"`            // 订单状态
		Doc_dep_id        int     `db:"doc_dep_id"`        // 医生部门ID
		Asst_dep_id       int     `db:"asst_dep_id"`       // 医助部门ID
		Pay_review_status int     `db:"pay_review_status"` // 支付审核状态
		Create_time       string  `db:"create_time"`       // 创建时间
	}

	sql = "SELECT id,doc_id,pat_id,pat_pro_id,record_ids,asst_id,total_money,pre_pay,pay_review_status,status,doc_dep_id,asst_dep_id,create_time FROM orders WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"
	var order []Order
	err = database.GetAll(sql, &order, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  order,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 病历列表 51
func Patient_records_list(w http.ResponseWriter, r *http.Request) {
	api_id := 51
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// ----------- 新增销售数据管理员后的药物筛选开始
	var attSql string
	var is_doc bool = false
	var is_asst bool = false
	if session.Values["role_ids"] == "3" || session.Values["role_ids"] == "9" {
		//医助（售前、售前）
		is_asst = true
		attSql += " AND asst_id = " + strconv.Itoa(session.Values["id"].(int))
	} else if session.Values["role_ids"] == "4" {
		//医生
		is_doc = true
		attSql += " AND doc_id =  " + strconv.Itoa(session.Values["id"].(int))
	}
	// 如果不是医生角色
	if !is_doc {
		doc_id := r.FormValue("doc_id")
		if doc_id != "" {
			attSql += " AND doc_id = " + doc_id
		} else {
			doc_dep_id := r.FormValue("doc_dep_id")
			if doc_dep_id != "" {
				attSql += " AND department_id = " + doc_dep_id
			}
		}
	}
	// 如果不是医助角色
	if !is_asst {
		asst_id := r.FormValue("asst_id")
		if asst_id != "" {
			attSql += " AND asst_id = " + asst_id
		} else {
			asst_dep_id := r.FormValue("asst_dep_id")
			if session.Values["role_ids"] == "2" {
				// 销售数据管理员
				if asst_dep_id != "" {
					// 判断前端传递的部门ID是否在dep_ids中
					asst_dep_ids := strings.Split(asst_dep_id, ",")
					dep_ids_arr := strings.Split(session.Values["dep_ids"].(string), ",")
					// 将dep_ids转换为map，便于快速查找
					dep_ids_map := make(map[string]bool)
					for _, id := range dep_ids_arr {
						dep_ids_map[id] = true
					}
					// 检查asst_dep_id中的每个元素是否都存在于dep_ids中
					for _, id := range asst_dep_ids {
						if !dep_ids_map[id] {
							common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
								"code": 500,
								"msg":  "您请求的部门中，有您无权查看的部门，这可能是您的权限已被调整 ( " + asst_dep_id + " vs " + session.Values["dep_ids"].(string) + " )",
							})
							return
						}
					}
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
					//attSql += " AND asst_dep_id IN (" + asst_dep_id + ")"
				} else {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + session.Values["dep_ids"].(string) + "))"
					//attSql += " AND asst_dep_id IN (" + session.Values["dep_ids"].(string) + ")"
				}
			} else {
				// 其它角色，如医生、超管、文员等；不是医助也不是销售数据管理员
				if asst_dep_id != "" {
					attSql += " AND asst_id IN (SELECT id FROM rbac_user WHERE department_id IN (" + asst_dep_id + "))"
					//attSql += " AND asst_dep_id IN (" + asst_dep_id + ")"
				}
			}
		}
	}
	// ----------- 新增销售数据管理员后的药物筛选结束
	pat_pro_id := r.FormValue("pat_pro_id")
	if pat_pro_id != "" {
		attSql += " AND pat_pro_id = " + pat_pro_id
	}
	record_ids := r.FormValue("record_ids")
	if record_ids != "" {
		attSql += " AND id IN (" + record_ids + ")"
	}
	status := r.FormValue("status")
	if status != "" {
		if strings.Contains(status, ",") {
			attSql += " AND status IN (" + status + ")"
		} else {
			attSql += " AND status = " + status
		}
	}
	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 10 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit
	// 总数
	var count int
	sql := "SELECT COUNT(id) FROM patient_records WHERE 1 " + attSql
	// fmt.Println(sql)
	err = database.GetOne(sql, &count)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "读取数据总数失败",
		})
		return
	}
	type Patient_records struct {
		ID            int    `db:"id"`            // 主键
		Ord_id        int    `db:"ord_id"`        //订单ID
		Pat_id        int    `db:"pat_id"`        // 用户帐号ID
		Pat_pro_id    int    `db:"pat_pro_id"`    // 用户资料id
		Department_id int    `db:"department_id"` // 科室ID
		Doc_id        int    `db:"doc_id"`        // 医生ID
		Asst_id       int    `db:"asst_id"`       // 医助
		Asst_dep_id   int    `db:"asst_dep_id"`   // 售前部门
		Pre_id        int    `db:"pre_id"`        // 绑定的处方ID
		Status        int    `db:"status"`        // 状态
		LAY_DISABLED  int    `db:"LAY_DISABLED"`  // 订单ID，LAYUI表格是否禁用
		Create_time   string `db:"create_time"`   // 建档时间
	}

	sql = "SELECT id, pat_id,pat_pro_id, department_id, doc_id,asst_id,asst_dep_id,ord_id,pre_id,status,case when status = 2 then false else true end as LAY_DISABLED,create_time FROM patient_records WHERE 1 " + attSql + " ORDER BY id DESC limit ? OFFSET ?"

	var patient_records []Patient_records
	err = database.GetAll(sql, &patient_records, limit, offset)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql, limit, offset),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "ok",
		"data":  patient_records,
		"count": count,
		"sql":   common.DebugSql(sql, limit, offset),
	})
}

// 处方ID2用户信息
func Pre_id2patient_profile(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	session, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	pre_id, _ := strconv.Atoi(r.FormValue("pre_id"))
	if pre_id <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "处方ID不得为空或小于等于0",
		})
		return
	}
	type PatientProfile struct {
		ID        int    `db:"id"`
		PID       int    `db:"pid"`
		Phone     string `db:"phone"`
		Name      string `db:"name"`
		Sex       int    `db:"sex"`
		Born_date string `db:"born_date"`
		Address   string `db:"address"`
	}
	sql := "select id,pid,phone,name,sex,born_date,address from patient_profile where id = (select pat_pro_id from prescription where id = ?)"
	var patient_profile PatientProfile
	err := database.GetRow(sql, &patient_profile, pre_id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询用户信息失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pre_id),
		})
		return
	}
	// 根据权限处理手机号显示方式
	if session.Values["role_ids"] != "1" {
		phone := patient_profile.Phone
		if len(phone) == 11 || len(phone) == 13 {
			patient_profile.Phone = phone[:5] + "****" + phone[len(phone)-2:]
		} else {
			patient_profile.Phone = "手机号码格式错误"
		}
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "查询用户信息成功",
		"data": patient_profile,
	})
}

// 生成腾讯云COS上传签名
func Get_cos_upload_sign(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 获取文件扩展名
	ext := r.FormValue("ext")
	if ext == "" {
		ext = "mp4" // 默认为mp4格式
	}

	// 生成随机文件名
	now := time.Now()
	timestamp := now.Unix()
	randomStr := common.RandStringRunes(8)
	fileName := fmt.Sprintf("%s%d_%s.%s", config.COS_BasePath, timestamp, randomStr, ext)

	// 使用腾讯云COS SDK生成预签名URL
	// 创建COS客户端
	u, _ := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", config.COS_Bucket, config.COS_Region))
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.Qcloud_Secret_Id,
			SecretKey: config.Qcloud_Secret_Key,
		},
	})

	ctx := context.Background()

	// 设置更长的有效期，确保上传大文件时不会过期，2小时后过期，请根据实际情况调整有效期
	expiration := time.Duration(7200) * time.Second // 2小时，2小时后过期，请根据实际情况调整有效期，单位为秒

	// 设置请求头，确保内容类型正确
	opt := &cos.PresignedURLOptions{
		Header: &http.Header{},
	}
	opt.Header.Add("Content-Type", "video/webm")

	// 生成预签名URL
	presignedURL, err := client.Object.GetPresignedURL(ctx, http.MethodPut, fileName, config.Qcloud_Secret_Id, config.Qcloud_Secret_Key, expiration, opt)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "生成预签名URL失败",
			"err":  err.Error(),
		})
		return
	}

	// 记录日志
	common.Add_log(fmt.Sprintf("生成COS上传签名，文件名：%s", fileName), r)

	// 返回预签名URL信息
	host := fmt.Sprintf("%s.cos.%s.myqcloud.com", config.COS_Bucket, config.COS_Region)
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "获取签名成功",
		"data": map[string]interface{}{
			"presignedURL": presignedURL.String(),
			"cosKey":       fileName,
			"cosHost":      host,
			"url":          fmt.Sprintf("https://%s/%s", host, fileName),
		},
	})
}

// 保存RTC视频记录
func Rtc_video_save(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 获取参数
	pid, err := strconv.Atoi(r.FormValue("pid"))
	if err != nil || pid <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "房间ID参数错误",
		})
		return
	}

	path := r.FormValue("path")
	if path == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "视频路径不能为空",
		})
		return
	}

	// 插入数据
	sql := "INSERT INTO rtc_videos (pid, path, create_time) VALUES (?, ?, NOW())"
	_, err = database.Query(sql, pid, path)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "保存视频记录失败",
			"err":  err.Error(),
		})
		return
	}

	// 记录日志
	common.Add_log(fmt.Sprintf("保存RTC视频记录，房间ID：%d，路径：%s", pid, path), r)

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "视频记录保存成功",
	})
}

// 获取RTC视频列表
func Rtc_videos_list(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 获取参数
	pid, err := strconv.Atoi(r.FormValue("pid"))
	if err != nil || pid <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "房间ID参数错误",
		})
		return
	}

	// 查询数据
	type RtcVideo struct {
		ID          int    `db:"id"`
		PID         int    `db:"pid"`
		Path        string `db:"path"`
		Create_time string `db:"create_time"`
	}

	sql := "SELECT id, pid, path, create_time FROM rtc_videos WHERE pid = ? ORDER BY create_time DESC"
	var videos []RtcVideo
	err = database.GetAll(sql, &videos, pid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "获取视频列表失败",
			"err":  err.Error(),
		})
		return
	}

	// 记录日志
	// common.Add_log(fmt.Sprintf("获取RTC视频列表，房间ID：%d", pid), r)

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"msg":   "获取视频列表成功",
		"data":  videos,
		"count": len(videos),
	})
}

// 删除RTC视频记录及云存储文件
func Rtc_videos_del(w http.ResponseWriter, r *http.Request) {
	api_id := config.NormalPerm
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}

	// 获取参数
	pid, err := strconv.Atoi(r.FormValue("pid"))
	if err != nil || pid <= 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "房间ID参数错误",
		})
		return
	}

	// 先查询所有需要删除的视频路径
	type RtcVideo struct {
		ID   int    `db:"id"`
		Path string `db:"path"`
	}
	sql := "SELECT id, path FROM rtc_videos WHERE pid = ?"
	var videos []RtcVideo
	err = database.GetAll(sql, &videos, pid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询视频记录失败",
			"err":  err.Error(),
		})
		return
	}

	// 创建COS客户端
	u, _ := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", config.COS_Bucket, config.COS_Region))
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.Qcloud_Secret_Id,
			SecretKey: config.Qcloud_Secret_Key,
		},
	})

	// 删除云存储中的视频文件
	for _, video := range videos {
		// 从完整URL中提取对象键名
		if video.Path != "" {
			// 解析URL获取对象键名
			parsedURL, err := url.Parse(video.Path)
			if err != nil {
				common.Add_log(fmt.Sprintf("解析URL失败，路径：%s，错误：%s", video.Path, err.Error()), r)
				continue // 跳过无效URL
			}

			// 从URL中提取对象键名
			// 例如：从 https://mst-1339976641.cos.ap-nanjing.myqcloud.com/rtc_videos/1747902206_N5sWwhcr.webm
			// 提取 rtc_videos/1747902206_N5sWwhcr.webm
			host := fmt.Sprintf("%s.cos.%s.myqcloud.com", config.COS_Bucket, config.COS_Region)
			fullPath := parsedURL.Host + parsedURL.Path
			objectKey := strings.TrimPrefix(fullPath, host+"/")

			// 记录日志
			common.Add_log(fmt.Sprintf("准备删除COS视频文件，URL：%s，提取的对象键名：%s", video.Path, objectKey), r)

			// 删除对象
			_, err = client.Object.Delete(context.Background(), objectKey)
			if err != nil {
				// 记录错误但继续处理
				common.Add_log(fmt.Sprintf("删除COS视频文件失败，对象键名：%s，错误：%s", objectKey, err.Error()), r)
			} else {
				common.Add_log(fmt.Sprintf("成功删除COS视频文件，对象键名：%s", objectKey), r)
			}
		}
	}

	// 删除数据库记录
	sql = "DELETE FROM rtc_videos WHERE pid = ?"
	_, err = database.Query(sql, pid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "删除视频记录失败",
			"err":  err.Error(),
		})
		return
	}

	// 记录日志
	// common.Add_log(fmt.Sprintf("删除RTC视频记录及云存储文件，房间ID：%d", pid), r)

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "删除视频记录成功",
	})
}

// 数据库连接池状态监控
func DB_pool_status(w http.ResponseWriter, r *http.Request) {
	// api_id := config.SystemPerm
	// _, isLogin := common.Check_Perm(w, r, api_id)
	// if !isLogin {
	// 	return
	// }
	stats := database.GetDBStats()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "数据库连接池状态获取成功",
		"data": stats,
	})
}
