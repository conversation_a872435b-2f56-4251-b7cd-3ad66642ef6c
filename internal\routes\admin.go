package routes

import (
	handlers "mstproject/internal/app/admin"
	"net/http"
)

// RegisterRoutes 注册所有的路由
func AdminRoutes(mux *http.ServeMux) {
	// 用户
	mux.HandleFunc("/admin/user/login", handlers.User_login)
	mux.HandleFunc("/admin/user/logout", handlers.User_logout)
	mux.HandleFunc("/admin/user/list_withpage", handlers.User_list_withpage)
	mux.HandleFunc("/admin/user/list", handlers.User_list)
	mux.HandleFunc("/admin/user/list_low", handlers.User_list_low)
	mux.HandleFunc("/admin/user/add", handlers.User_add)
	mux.HandleFunc("/admin/user/edit", handlers.User_edit)
	mux.HandleFunc("/admin/user/del", handlers.User_del)
	mux.HandleFunc("/admin/user/bind_roles", handlers.User_bind_roles)
	mux.HandleFunc("/admin/user/check_bind_wx", handlers.Check_bind_wx)
	mux.HandleFunc("/admin/user/unbind_wx", handlers.Unbind_wx)

	// 角色
	mux.HandleFunc("/admin/roles/list", handlers.Roles_list)
	mux.HandleFunc("/admin/roles/add", handlers.Roles_add)
	mux.HandleFunc("/admin/roles/edit", handlers.Roles_edit)
	mux.HandleFunc("/admin/roles/del", handlers.Roles_del)
	mux.HandleFunc("/admin/roles/bind_perm", handlers.Roles_bind_perm)
	// 权限
	mux.HandleFunc("/admin/perm/list", handlers.Perm_list)
	mux.HandleFunc("/admin/perm/add", handlers.Perm_add)
	mux.HandleFunc("/admin/perm/edit", handlers.Perm_edit)
	mux.HandleFunc("/admin/perm/del", handlers.Perm_del)
	mux.HandleFunc("/admin/perm/detail", handlers.Perm_detail)
	mux.HandleFunc("/admin/perm/get_par_perms", handlers.Get_par_perms)
	mux.HandleFunc("/admin/perm/change_perm_sort", handlers.Change_perm_sort)

	// 药品分类
	mux.HandleFunc("/admin/drug_category/list", handlers.Drug_category_list)
	mux.HandleFunc("/admin/drug_category/add", handlers.Drug_category_add)
	mux.HandleFunc("/admin/drug_category/detail", handlers.Drug_category_detail)
	mux.HandleFunc("/admin/drug_category/edit", handlers.Drug_category_edit)
	mux.HandleFunc("/admin/drug_category/del", handlers.Drug_category_del)

	// 部门（科室）
	mux.HandleFunc("/admin/department/list", handlers.Department_list)
	mux.HandleFunc("/admin/department/detail", handlers.Department_detail)
	mux.HandleFunc("/admin/department/add", handlers.Department_add)
	mux.HandleFunc("/admin/department/edit", handlers.Department_edit)
	mux.HandleFunc("/admin/department/del", handlers.Department_del)
	mux.HandleFunc("/admin/department/salemaster_bind_department_list", handlers.Salemaster_bind_department_list)
	mux.HandleFunc("/admin/department/salemaster_bind_department_bind", handlers.Salemaster_bind_department_bind)

	// 患者帐号
	mux.HandleFunc("/admin/patient_account/list", handlers.Patient_account_list)
	// 判断患者帐号微信绑定情况 - 患者列表页使用
	mux.HandleFunc("/admin/patient_account/check_wxbind", handlers.Patient_account_check_wxbind)
	mux.HandleFunc("/admin/patient_account/detail", handlers.Patient_account_detail)
	// mux.HandleFunc("/admin/patient_account/add", handlers.Patient_account_add)
	mux.HandleFunc("/admin/patient_account/edit", handlers.Patient_account_edit)
	mux.HandleFunc("/admin/patient_account/transfer", handlers.Patient_account_transfer)
	// 分配售后客服
	mux.HandleFunc("/admin/patient_account/asst_transfer", handlers.Patient_account_asst_transfer)
	// 超级管理员转用户
	mux.HandleFunc("/admin/patient_account/Transfer_user", handlers.Transfer_user)

	// 患者档案
	mux.HandleFunc("/admin/patient_profile/list", handlers.Patient_profile_list)
	mux.HandleFunc("/admin/patient_profile/detail", handlers.Patient_profile_detail)
	mux.HandleFunc("/admin/patient_profile/patient_phone2id", handlers.Patient_phone2id)
	mux.HandleFunc("/admin/patient_profile/patient_profile_phone2id", handlers.Patient_profile_phone2id)
	mux.HandleFunc("/admin/patient_profile/patient_profile_id2name", handlers.Patient_profile_id2name)
	mux.HandleFunc("/admin/patient_profile/patient_profile_ids2name", handlers.Patient_profile_ids2name)
	mux.HandleFunc("/admin/patient_profile/add", handlers.Patient_profile_add)
	mux.HandleFunc("/admin/patient_profile/before_add", handlers.Patient_profile_before_add)
	mux.HandleFunc("/admin/patient_profile/edit", handlers.Patient_profile_edit)
	mux.HandleFunc("/admin/patient_profile/del", handlers.Patient_profile_del)
	mux.HandleFunc("/admin/patient_profile/qrcode_status_change", handlers.Patient_profile_qrcode_status_change)
	mux.HandleFunc("/admin/patient_profile/links", handlers.Patient_profile_links)

	// 病历信息
	mux.HandleFunc("/admin/patient_records/list", handlers.Patient_records_list)
	mux.HandleFunc("/admin/patient_records/del", handlers.Patient_records_del)
	mux.HandleFunc("/admin/patient_records/count", handlers.Patient_records_count)
	mux.HandleFunc("/admin/patient_records/detail", handlers.Patient_records_detail)
	mux.HandleFunc("/admin/patient_records/add", handlers.Patient_records_add)
	mux.HandleFunc("/admin/patient_records/edit", handlers.Patient_records_edit)
	mux.HandleFunc("/admin/patient_records/review", handlers.Patient_records_review)

	// 病历跟进
	mux.HandleFunc("/admin/patient_records_follow/list", handlers.Patient_records_follow_list)
	mux.HandleFunc("/admin/patient_records_follow/detail", handlers.Patient_records_follow_detail)
	mux.HandleFunc("/admin/patient_records_follow/add", handlers.Patient_records_follow_add)

	// 处方管理
	mux.HandleFunc("/admin/prescription/list", handlers.Prescription_list)
	mux.HandleFunc("/admin/prescription/detail", handlers.Prescription_detail)
	mux.HandleFunc("/admin/prescription/drug", handlers.Prescription_drug)
	mux.HandleFunc("/admin/prescription/add", handlers.Prescription_add)
	mux.HandleFunc("/admin/prescription/edit", handlers.Prescription_edit)
	mux.HandleFunc("/admin/prescription/del", handlers.Prescription_del)
	mux.HandleFunc("/admin/prescription/delete_drug", handlers.Prescription_Delete_drug)
	mux.HandleFunc("/admin/prescription/verify", handlers.Prescription_verify)
	mux.HandleFunc("/admin/prescription/list_verify", handlers.Prescription_list_verify)
	mux.HandleFunc("/admin/prescription/list_reject", handlers.Prescription_list_reject)
	mux.HandleFunc("/admin/prescription/edit_drug_save", handlers.Prescription_edit_drug_save)
	mux.HandleFunc("/admin/prescription/re_verify", handlers.Prescription_re_verify)
	mux.HandleFunc("/admin/prescription/dispense_save", handlers.Prescription_dispense_save)

	//新建处方时，使用成品药
	mux.HandleFunc("/admin/prescription/add_with_finished_drug", handlers.Prescription_add_with_finished_drug)

	// 订单管理
	mux.HandleFunc("/admin/order/list", handlers.Order_list)
	mux.HandleFunc("/admin/order/list_express", handlers.Order_list_express)
	mux.HandleFunc("/admin/order/clerk_aftersales_list", handlers.Order_clerk_aftersales_list)
	mux.HandleFunc("/admin/order/clerk_aftersales_wait_list", handlers.Order_clerk_aftersales_wait_list)
	mux.HandleFunc("/admin/order/exist_by_record", handlers.Order_exist_by_record)
	mux.HandleFunc("/admin/order/detail", handlers.Order_detail)
	mux.HandleFunc("/admin/order/add", handlers.Order_add)
	mux.HandleFunc("/admin/order/exist_by_record_check", handlers.Order_exist_by_record_check)
	mux.HandleFunc("/admin/order/edit", handlers.Order_edit)
	mux.HandleFunc("/admin/order/edit_gift_save", handlers.Order_edit_gift_save)
	mux.HandleFunc("/admin/order/del", handlers.Order_del)
	// mux.HandleFunc("/admin/patient_profile/get_address_by_record_id", handlers.Patient_profile_get_address_by_record_id) - 根据病历ID获取用户收货地址 - 地址已转至订单表
	mux.HandleFunc("/admin/order/gift_data", handlers.Order_gift_data)
	// 根据病历集合，求患者集合
	mux.HandleFunc("/admin/patient_profile/get_patient_ids_by_record_ids", handlers.Patient_profile_get_patient_ids_by_record_ids)
	//根据处方IDS查询其对应的各处方状态,如果查不到对应处方,证明处方没开具,返回状态0
	mux.HandleFunc("/admin/order/get_pre_status_by_record_ids", handlers.Get_pre_status_by_record_ids)

	// 订金审核
	mux.HandleFunc("/admin/order/pay_review_pre", handlers.Order_pay_review_pre)
	// 尾款审核
	mux.HandleFunc("/admin/order/pay_review_final", handlers.Order_pay_review_final)
	// 尾款明细
	mux.HandleFunc("/admin/order/pay_detail", handlers.Order_pay_detail)
	// 尾款更新
	mux.HandleFunc("/admin/order/pay_update_final", handlers.Order_pay_update_final)

	// 仓库(药品原材料)管理
	mux.HandleFunc("/admin/warehouse_drug/list", handlers.Warehouse_drug_list)
	mux.HandleFunc("/admin/warehouse_drug/detail", handlers.Warehouse_drug_detail)
	mux.HandleFunc("/admin/warehouse_drug/add", handlers.Warehouse_drug_add)
	mux.HandleFunc("/admin/warehouse_drug/in", handlers.Warehouse_drug_in)
	mux.HandleFunc("/admin/warehouse_drug/out", handlers.Warehouse_drug_out)
	mux.HandleFunc("/admin/warehouse_drug/check_by_id", handlers.Warehouse_drug_check_by_id)
	mux.HandleFunc("/admin/warehouse_gift/check_by_id", handlers.Warehouse_gift_check_by_id)

	// 出入库日志(药品原材料)
	mux.HandleFunc("/admin/warehouse_drug_log/list", handlers.Warehouse_drug_log_list)
	mux.HandleFunc("/admin/warehouse_drug_log/detail", handlers.Warehouse_drug_log_detail)

	// 仓库(赠品)管理
	mux.HandleFunc("/admin/warehouse_gifts/list", handlers.Warehouse_gifts_list)
	mux.HandleFunc("/admin/warehouse_gifts/detail", handlers.Warehouse_gifts_detail)
	mux.HandleFunc("/admin/warehouse_gifts/add", handlers.Warehouse_gifts_add)
	mux.HandleFunc("/admin/warehouse_gifts/in", handlers.Warehouse_gifts_in)
	mux.HandleFunc("/admin/warehouse_gifts/out", handlers.Warehouse_gifts_out)

	// 出入库日志(赠品)
	mux.HandleFunc("/admin/warehouse_gifts_log/list", handlers.Warehouse_gifts_log_list)
	mux.HandleFunc("/admin/warehouse_gifts_log/detail", handlers.Warehouse_gifts_log_detail)

	// 仓库(成品退货)管理
	mux.HandleFunc("/admin/warehouse_finisheddrug/list", handlers.Warehouse_finisheddrug_list)
	mux.HandleFunc("/admin/warehouse_finisheddrug/detail", handlers.Warehouse_finisheddrug_detail)
	mux.HandleFunc("/admin/warehouse_finisheddrug/add", handlers.Warehouse_finisheddrug_add)                           //新增成品库存
	mux.HandleFunc("/admin/warehouse_finisheddrug/in", handlers.Warehouse_finisheddrug_in)                             //补货入库
	mux.HandleFunc("/admin/warehouse_finisheddrug/out", handlers.Warehouse_finisheddrug_out)                           //退货出库
	mux.HandleFunc("/admin/warehouse_finisheddrug/list_choose_drug", handlers.Warehouse_finisheddrug_list_choose_drug) //开方时，选择成品药时的接口

	// 出入库日志(退货成品)
	mux.HandleFunc("/admin/warehouse_finisheddrug_log/list", handlers.Warehouse_finisheddrug_log_list)
	mux.HandleFunc("/admin/warehouse_finisheddrug_log/detail", handlers.Warehouse_finisheddrug_log_detail)

	// 供应商ID2NAME
	mux.HandleFunc("/admin/supplier_drug/sup_id2name", handlers.Sup_id2name)

	// 供应商管理
	mux.HandleFunc("/admin/supplier_drug/list", handlers.Supplier_drug_list)
	mux.HandleFunc("/admin/supplier_drug/add", handlers.Supplier_drug_add)
	mux.HandleFunc("/admin/supplier_drug/edit", handlers.Supplier_drug_edit)
	mux.HandleFunc("/admin/supplier_drug/detail", handlers.Supplier_drug_detail)
	mux.HandleFunc("/admin/supplier_drug/del", handlers.Supplier_drug_del)

	// 药材管理
	mux.HandleFunc("/admin/drug/list", handlers.Drug_list)
	mux.HandleFunc("/admin/drug/add", handlers.Drug_add)
	mux.HandleFunc("/admin/drug/edit", handlers.Drug_edit)
	mux.HandleFunc("/admin/drug/detail", handlers.Drug_detail)
	mux.HandleFunc("/admin/drug/del", handlers.Drug_del)

	// 赠品管理
	mux.HandleFunc("/admin/gift/list", handlers.Gift_list)
	mux.HandleFunc("/admin/gift/add", handlers.Gift_add)
	mux.HandleFunc("/admin/gift/edit", handlers.Gift_edit)
	mux.HandleFunc("/admin/gift/detail", handlers.Gift_detail)
	mux.HandleFunc("/admin/gift/del", handlers.Gift_del)

	// 用户一码通
	mux.HandleFunc("/admin/codebar/patient_profile_qrcode", handlers.Patient_profile_qrcode)
	// 处方一码通
	mux.HandleFunc("/admin/codebar/prescription_qrcode", handlers.Prescription_qrcode)

	// 线上诊室
	mux.HandleFunc("/admin/rtc_room/list", handlers.Rtc_room_list)
	mux.HandleFunc("/admin/rtc_room/set", handlers.Rtc_room_status_set)
	mux.HandleFunc("/admin/rtc_room/del", handlers.Rtc_room_del)
	mux.HandleFunc("/admin/rtc_room/add", handlers.Rtc_room_add)
	mux.HandleFunc("/admin/rtc_room/detail", handlers.Rtc_room_detail)
	mux.HandleFunc("/admin/rtc_room/exist_by_record", handlers.Rtc_room_exist_by_record)
	mux.HandleFunc("/admin/rtc_room/schedule", handlers.Rtc_room_schedule)
	mux.HandleFunc("/admin/rtc_room/get_trtc_config", handlers.Rtc_room_get_trtc_config)

	// 文章分类
	mux.HandleFunc("/admin/article_category/list", handlers.Article_category_list)
	mux.HandleFunc("/admin/article_category/add", handlers.Article_category_add)
	mux.HandleFunc("/admin/article_category/detail", handlers.Article_category_detail)
	mux.HandleFunc("/admin/article_category/edit", handlers.Article_category_edit)
	mux.HandleFunc("/admin/article_category/del", handlers.Article_category_del)

	// 文章管理
	mux.HandleFunc("/admin/article/list", handlers.Article_list)
	mux.HandleFunc("/admin/article/add", handlers.Article_add)
	mux.HandleFunc("/admin/article/detail", handlers.Article_detail)
	mux.HandleFunc("/admin/article/edit", handlers.Article_edit)
	mux.HandleFunc("/admin/article/del", handlers.Article_del)

	//系统操作日志
	mux.HandleFunc("/admin/system_logs/list", handlers.System_logs_list)
	mux.HandleFunc("/admin/system_logs/detail", handlers.System_logs_detail)
	mux.HandleFunc("/admin/system_logs/del", handlers.System_logs_del)

	// 通用图片上传接口
	mux.HandleFunc("/admin/upload_normal_pic", handlers.Upload_normal_pic)
	mux.HandleFunc("/admin/normal_pic_del", handlers.Normal_pic_del)
	mux.HandleFunc("/admin/normal_pic_list", handlers.Normal_pic_list)

	// 通用文件上传接口
	mux.HandleFunc("/admin/upload_normal_file", handlers.Upload_normal_file)
	mux.HandleFunc("/admin/normal_file_del", handlers.Normal_file_del)

	// 系统参数显示
	mux.HandleFunc("/admin/sys_config/detail", handlers.Sys_config_detail)
	// 保存音视频自动录制参数
	mux.HandleFunc("/admin/sys_config/sys_config_rtc_setting_edit", handlers.Sys_config_rtc_setting_edit)
	// 读取音视频自动录制参数
	mux.HandleFunc("/admin/sys_config/rtc_auto_record_detail", handlers.Sys_config_rtc_auto_record_detail)
	// NAS配置参数相关
	mux.HandleFunc("/admin/Nas_config/edit", handlers.Nas_config_edit)

	//患者端小程序首页顶部幻灯片
	mux.HandleFunc("/admin/weichat_patient_slides/list", handlers.Weichat_patient_slides_list)
	mux.HandleFunc("/admin/weichat_patient_slides/edit", handlers.Weichat_patient_slides_edit)
	mux.HandleFunc("/admin/get_wx_acode", handlers.Get_wx_acode)
	mux.HandleFunc("/admin/get_wx_bind_user_acode", handlers.Get_wx_bind_user_acode)

	// 当前用户权限下可访问的菜单、按钮、权限等资源
	mux.HandleFunc("/admin/res/current", handlers.Res_current)
	mux.HandleFunc("/admin/res/current_refresh", handlers.Res_current_refresh)

	// 库存盘点
	mux.HandleFunc("/admin/stock_take/list", handlers.Stock_take_list)
	mux.HandleFunc("/admin/stock_take/add", handlers.Stock_take_add)
	mux.HandleFunc("/admin/stock_take/edit", handlers.Stock_take_edit)
	mux.HandleFunc("/admin/stock_take/del", handlers.Stock_take_del)
	mux.HandleFunc("/admin/stock_take/detail", handlers.Stock_take_detail)

	// 库存ID2NAME
	mux.HandleFunc("/admin/tools/warehouse2name", handlers.Warehouse2productname)
	mux.HandleFunc("/admin/tools/warehouse2productname_finisheddrug", handlers.Warehouse2productname_finisheddrug)
	// 通用处方是否入库查询
	mux.HandleFunc("/admin/tools/find_prescription_to_warehouse_by_id", handlers.Find_prescription_to_warehouse_by_id)

	// 查看指定订单状态，是否允许其旗下病历允许开处方
	mux.HandleFunc("/admin/tools/check_order_status_to_prescription", handlers.Check_order_status_to_prescription)

	// 用户解绑微信小程序
	mux.HandleFunc("/patient/un_bind_user", handlers.Un_bind_user)

	// 根据订单号查询物流表中的数据：
	mux.HandleFunc("/admin/express_info_by_ord_id", handlers.Express_info_by_ord_id)

	// 印章上传
	mux.HandleFunc("/admin/update_stamp", handlers.Update_stamp)

	// Echarts图表数据接口-病历
	mux.HandleFunc("/admin/charts_records", handlers.Charts_Records)
	// Echarts图表数据接口-订单
	mux.HandleFunc("/admin/charts_orders", handlers.Charts_Orders)

	// 图文沟通模块
	mux.HandleFunc("/admin/visit_logs/list", handlers.Visit_logs_list)
	mux.HandleFunc("/admin/visit_logs/add", handlers.Visit_logs_add)
	mux.HandleFunc("/admin/visit_logs/del", handlers.Visit_logs_del)

	// 音视频沟通模块
	mux.HandleFunc("/admin/video_logs/list", handlers.Video_logs_list)
	mux.HandleFunc("/admin/video_logs/del", handlers.Video_logs_del)

	// 电话沟通模块
	mux.HandleFunc("/admin/phone_logs/list", handlers.Phone_logs_list)
	mux.HandleFunc("/admin/phone_logs/add", handlers.Phone_logs_add)
	mux.HandleFunc("/admin/phone_logs/del", handlers.Phone_logs_del)
	// 处方ID2用户信息
	mux.HandleFunc("/admin/pre_id2patient_profile", handlers.Pre_id2patient_profile)

	// 腾讯云COS相关
	mux.HandleFunc("/admin/cos/get_upload_sign", handlers.Get_cos_upload_sign)
	mux.HandleFunc("/admin/rtc_video/save", handlers.Rtc_video_save)   // 添加保存RTC视频URL的路由
	mux.HandleFunc("/admin/rtc_videos/list", handlers.Rtc_videos_list) // 获取RTC视频列表
	mux.HandleFunc("/admin/rtc_videos/del", handlers.Rtc_videos_del)   // 删除RTC视频记录及云存储文件

	// 系统监控
	mux.HandleFunc("/admin/db_pool_status", handlers.DB_pool_status) // 数据库连接池状态监控
}
