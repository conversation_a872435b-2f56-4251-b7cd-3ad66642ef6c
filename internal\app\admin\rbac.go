package admin

import (
	"fmt"
	"mstproject/pkg/common"
	"mstproject/pkg/config"
	"mstproject/pkg/database"
	"net/http"
	"os"
	"regexp"
	"slices"
	"strconv"
	"strings"
)

// 角色列表
func Roles_list(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	type Role struct {
		ID       int    `db:"id"`
		Role     string `db:"role"`
		Sys      int    `db:"sys"`
		Perm_ids string `db:"perm_ids"`
	}
	var role []Role

	ids := r.FormValue("id")
	var sql strings.Builder
	var args []interface{}

	sql.WriteString(`
		SELECT 
			a.id, 
			a.role, 
			a.sys,
			IFNULL(GROUP_CONCAT(b.perm_id ORDER BY b.perm_id ASC), '0') AS perm_ids
		FROM 
			rbac_role AS a
		LEFT JOIN 
			rbac_role_perm AS b
		ON 
			a.id = b.role_id
	`)

	if ids != "" {
		sql.WriteString(" WHERE a.id IN (")
		idList := strings.Split(ids, ",")
		for i, id := range idList {
			if i > 0 {
				sql.WriteString(", ")
			}
			sql.WriteString("?")
			args = append(args, id) // 添加参数化 ID
		}
		sql.WriteString(")") // 关闭 IN 列表
	}

	sql.WriteString(`
		GROUP BY 
			a.id, a.role, a.sys;
	`)

	// 参数化查询
	err := database.GetAll(sql.String(), &role, args...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据库查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql.String()),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": role,
	})
}

// 添加角色
func Roles_add(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	Role, err := common.CheckStr(r.FormValue("role"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	// 插入前先检查有没有重复的角色
	sql := "SELECT id FROM rbac_role WHERE role = ? limit 1"
	var id int
	err = database.GetOne(sql, &id, Role)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "查询错误或角色已存在，角色ID：" + strconv.Itoa(id),
		})
		return
	}
	// 主逻辑
	sql = "insert into rbac_role (role) values (?)"
	_, err = database.Query(sql, Role)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据添加失败",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "数据添加成功",
	})
}

// 修改角色
func Roles_edit(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	Role, err := common.CheckStr(r.FormValue("role"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	// 内置角色不允许修改，内置角色标识为sys=1
	sql := "select sys as sys_flag from rbac_role where id = ? limit 1"
	var sys_flag int
	err = database.GetOne(sql, &sys_flag, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询错误",
		})
		return
	}
	if sys_flag == 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "内置角色不允许修改",
		})
		return
	}
	// 主逻辑
	sql = "update rbac_role set role = ? where id = ?"
	result, err := database.Query(sql, Role, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据更新失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "操作执行成功",
		"RowsAffected": RowsAffected,
		// "sql":  common.DebugSql(sql, Role, id),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改角色：%d", id), r)
	}
}

// 删除角色
func Roles_del(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	// 内置角色不允许删除，内置角色标识为sys=1
	sql := "select sys as sys_flag from rbac_role where id = ? limit 1"
	var sys_flag int
	err = database.GetOne(sql, &sys_flag, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询错误",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	if sys_flag == 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "内置角色不允许删除",
		})
		return
	}
	sql = "delete from rbac_role where id = ?"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据删除失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "操作执行成功",
		"RowsAffected": RowsAffected,
		// "sql":    common.DebugSql(sql, id),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除角色：%d", id), r)
	}
}

// 角色绑定权限
func Roles_bind_perm(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	sql := "delete from rbac_role_perm where role_id = ?"
	_, err = database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据绑定前的删除失败",
		})
		return
	}

	perm_ids := r.FormValue("perm_ids")
	if perm_ids == "" {
		common.JSONResponse(w, http.StatusOK, map[string]interface{}{
			"code": 200,
			"msg":  "您已将当前角色的权限全部取消",
		})
		return
	}

	perm_ids_array := strings.Split(perm_ids, ",")

	var args []interface{}
	for _, perm_id := range perm_ids_array {
		args = append(args, id, perm_id)
	}
	sql = "insert into rbac_role_perm (role_id, perm_id) values "
	for i := 0; i < len(args); i += 2 {
		sql += "(?, ?)"
		if i+2 < len(args) {
			sql += ","
		}
	}
	_, err = database.Query(sql, args...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "角色绑定权限 - 失败",
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "角色绑定权限 - 成功",
	})
	common.Add_log(fmt.Sprintf("角色绑定权限：%d", id), r)
}

// 权限列表
func Perm_list(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	type Perm struct {
		ID       int    `db:"id"`
		Pid      int    `db:"pid"`
		Name     string `db:"name"`
		Url      string `db:"url"`
		Api_path string `db:"api_path"`
		Icon     string `db:"icon"`
		Built_in int    `db:"built_in"`
		Type     int    `db:"type"`
		Sort     int    `db:"sort"`
	}
	var perm []Perm
	sql := "SELECT id,pid,name,url,api_path,icon,built_in,type,sort FROM rbac_perm order by sort desc,id desc"
	err := database.GetAll(sql, &perm)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据库查询失败",
			"err":  err.Error(),
			// "sql":  common.DebugSql(sql),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": perm,
	})
}

// 添加权限
func Perm_add(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	pid := r.FormValue("pid")
	if pid == "" {
		pid = "0"
	}
	name := r.FormValue("name")
	url := r.FormValue("url")
	if url == "" || pid == "0" {
		url = "0"
	}
	api_path := r.FormValue("api_path")
	if api_path == "" {
		api_path = "0"
	}
	icon := r.FormValue("icon")
	if icon == "" {
		icon = "0"
	}
	type_ := r.FormValue("type")
	if name == "" || type_ == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	typeInt, err := strconv.Atoi(type_)
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	var maxSort int
	err = database.GetOne("SELECT max(IFNULL(sort, 0)) + 10 as max_sort FROM rbac_perm", &maxSort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询排序值失败",
			"err":  err.Error(),
		})
		return
	}
	sql := "insert into rbac_perm(pid,name,url,api_path,icon,type,sort) values (?,?,?,?,?,?,?)"
	result, err := database.Query(sql, pid, name, url, api_path, icon, typeInt, maxSort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据添加失败",
			"err":  err.Error(),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "数据添加成功",
		"RowsAffected": RowsAffected,
	})
	common.Add_log(fmt.Sprintf("添加权限：%s", name), r)
}

// 修改权限
func Perm_edit(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id := r.FormValue("id")
	if id == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	params := []interface{}{}
	attSql := ""
	// pid,name,url,api_path,icon,built_in,type,sort
	pid := r.FormValue("pid")
	if pid != "" {
		attSql += ", pid = ?"
		params = append(params, pid)
	}
	name := r.FormValue("name")
	if name != "" {
		attSql += ", name = ?"
		params = append(params, name)
	}
	url := r.FormValue("url")
	if url != "" && url != "无" {
		attSql += ", url = ?"
		params = append(params, url)
	}
	api_path := r.FormValue("api_path")
	if api_path != "" && api_path != "无" {
		attSql += ", api_path = ?"
		params = append(params, api_path)
	}
	icon := r.FormValue("icon")
	if icon != "" && icon != "无" {
		attSql += ", icon = ?"
		params = append(params, icon)
	}
	built_in := r.FormValue("built_in")
	if built_in != "" {
		attSql += ", built_in = ?"
		params = append(params, built_in)
	}
	type_ := r.FormValue("type")
	if type_ != "" {
		attSql += ", type = ?"
		params = append(params, type_)
	}
	sort := r.FormValue("sort")
	if sort != "" {
		attSql += ", sort = ?"
		params = append(params, sort)
	}
	status := r.FormValue("status")
	if status != "" {
		attSql += ", status = ?"
		params = append(params, status)
	}
	if len(params) == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "您没有提交任何修改的数据",
		})
		return
	}
	// 在参数列表中添加id
	params = append(params, id)
	attSql = attSql[1:] // 去掉开头的逗号
	sql := "update rbac_perm set " + attSql + " where id = ?"
	result, err := database.Query(sql, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据更新失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, params...),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "操作执行成功",
		"RowsAffected": RowsAffected,
		"sql":          common.DebugSql(sql, params...),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改权限：%s", id), r)
	}
}

func Change_perm_sort(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id := r.FormValue("id")
	action := r.FormValue("action")
	pid := r.FormValue("pid")
	// 检查 id 是否为数字类型
	idInt, err := strconv.Atoi(id)
	if id == "" || pid == "" || err != nil || (action != "1" && action != "-1") {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	// 查询所有权限的排序值
	sql := "SELECT id, sort FROM rbac_perm where pid = ? ORDER BY sort DESC" // 按 sort 倒序排列
	var perm []struct {
		ID   int `db:"id"`
		Sort int `db:"sort"`
	}
	err = database.GetAll(sql, &perm, pid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据库查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pid),
		})
		return
	}
	// 找到当前节点的索引
	var currentIndex int = -1
	for i, v := range perm {
		if v.ID == idInt {
			currentIndex = i
			break
		}
	}
	if currentIndex == -1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "未找到对应的权限节点",
		})
		return
	}
	// 找到要交换的节点的索引
	var targetIndex int
	if action == "1" {
		// 向上移动，与前一个节点交换
		if currentIndex == 0 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "已经是第一个节点，无法继续上移",
			})
			return
		}
		targetIndex = currentIndex - 1
	} else {
		// 向下移动，与后一个节点交换
		if currentIndex == len(perm)-1 {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  "已经是最后一个节点，无法继续下移",
			})
			return
		}
		targetIndex = currentIndex + 1
	}
	// 交换排序值
	currentSort := perm[currentIndex].Sort
	targetSort := perm[targetIndex].Sort
	// 构建事务 SQL
	sql = "UPDATE rbac_perm SET sort = CASE id WHEN ? THEN ? WHEN ? THEN ? END WHERE id IN (?, ?)"
	args := []interface{}{idInt, targetSort, perm[targetIndex].ID, currentSort, idInt, perm[targetIndex].ID}
	result, err := database.Query(sql, args...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据库更新失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, args...),
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":         200,
		"msg":          "操作执行成功",
		"RowsAffected": RowsAffected,
		"sql":          common.DebugSql(sql, args...),
	})
	// 记录日志
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改权限排序：%d", idInt), r)
	}
}

// 按PID获取权限对应资源
func Get_par_perms(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	pid := r.FormValue("pid")
	if pid == "" {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "参数错误",
		})
		return
	}
	// 主逻辑开始
	type Perm struct {
		ID     int    `db:"id"`
		Name   string `db:"name"`
		Icon   string `db:"icon"`
		Sort   int    `db:"sort"`
		Status int    `db:"status"`
	}
	var perm []Perm
	sql := "SELECT id,name,icon,sort,status FROM rbac_perm where pid = ? order by sort desc,id desc"
	err := database.GetAll(sql, &perm, pid)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据库查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, pid),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": perm,
	})
}

// 权限详情
func Perm_detail(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	type Perm struct {
		ID       int    `db:"id"`
		Pid      int    `db:"pid"`
		Name     string `db:"name"`
		Url      string `db:"url"`
		Api_path string `db:"api_path"`
		Icon     string `db:"icon"`
		Built_in int    `db:"built_in"`
		Type     int    `db:"type"`
		Sort     int    `db:"sort"`
		Status   int    `db:"status"`
	}
	var perm Perm
	sql := "SELECT id,pid,name,url,api_path,icon,built_in,type,sort,status FROM rbac_perm where id = ?"
	err = database.GetOne(sql, &perm, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据库查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql, id),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": perm,
	})
}

// 删除权限
func Perm_del(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	if id < 124 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "测试阶段，只允许删除权限ID大于123的权限。",
		})
		return
	}
	// sql := "delete from rbac_perm where id = ?"
	// 此处应该使用事务

	// 删除分类，同时删除分类下的文章，使用事务操作
	sqls := []database.SQLExec{
		{Query: "delete from rbac_perm where id = ?",
			Args: []interface{}{
				id,
			}},
		{Query: "delete from rbac_perm where pid = ?",
			Args: []interface{}{
				id,
			}},
	}
	RowsAffected, err := database.ExecuteTransaction_with_affectedRows(sqls)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "权限表删除资源失败",
			"err":  err.Error(),
		})
		return
	}
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  fmt.Sprintf("权限表删除资源成功，影响行数: %d", RowsAffected),
	})
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("权限表删除资源：%d，影响行数：%d", id, RowsAffected), r)
	}
}

// 低权限用户列表，用于普通角色筛选数据时使用的岗位角色用户筛选ID
func User_list_low(w http.ResponseWriter, r *http.Request) {
	api_id := 149
	_, isLogin := common.Check_Perm(w, r, api_id)
	if !isLogin {
		return
	}
	// 主逻辑开始
	attrSql := ""
	//ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err == nil && id > 0 {
		attrSql += " and a.id = " + strconv.Itoa(id)
	}
	//新增ID集合，以逗号隔开的ID，如:1,2,3
	id_list := r.FormValue("user_id_list")
	if id_list != "" {
		attrSql += " and a.id in (" + id_list + ")"
	}
	// 部门（科室）ID
	department_id, err := common.CheckInt(r.FormValue("department_id"))
	if err == nil && department_id > 0 {
		attrSql += " and a.department_id = " + strconv.Itoa(department_id)
	}
	// 部门（科室）IDS集合
	department_ids := r.FormValue("department_ids")
	if department_ids != "" {
		attrSql += " and a.department_id in (" + department_ids + ")"
	}
	// 角色ID
	role_id, err := common.CheckInt(r.FormValue("role_id"))
	if err == nil && role_id > 0 {
		attrSql += " and c.id = " + strconv.Itoa(role_id)
	}
	type User struct {
		ID            int    `db:"id"`
		Name          string `db:"NAME"`
		Status        int    `db:"STATUS"`
		Role_id       string `db:"role_id"`
		Department_id int    `db:"department_id"`
		Sort          int    `db:"sort"`
	}
	sql := `
		SELECT
			a.id,
			a.NAME,
			a.STATUS,
			IFNULL(GROUP_CONCAT(DISTINCT c.id SEPARATOR ','), -1) AS role_id,
			a.department_id,
			a.sort
		FROM
			rbac_user AS a
			LEFT JOIN rbac_user_roles AS b ON a.id = b.user_id
			LEFT JOIN rbac_role AS c ON b.role_id = c.id
		WHERE 1
			` + attrSql + `
		GROUP BY
			a.id, a.NAME, a.STATUS, a.department_id, a.sort
		ORDER BY 
			CASE WHEN a.id = 1 THEN 0 ELSE 1 END,
			a.sort DESC, a.id DESC
		`
	var user []User
	err = database.GetAll(sql, &user)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":  500,
			"msg":   "数据库查询失败",
			"error": err.Error(),
			"sql":   common.DebugSql(sql),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": user,
		"sql":  common.DebugSql(sql),
	})
}

// user用户列表 - 带分页
func User_list_withpage(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	// 分页参数
	limit, err := strconv.Atoi(r.FormValue("limit"))
	if err != nil || limit < 1 {
		limit = 10
	}
	page, err := strconv.Atoi(r.FormValue("page"))
	if err != nil || page < 1 {
		page = 1
	}
	offset := (page - 1) * limit

	// 主逻辑开始
	var params []any
	attrSql := ""

	//ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err == nil && id > 0 {
		attrSql += " and a.id = ?"
		params = append(params, id)
	}

	// 部门（科室）ID
	department_id, err := common.CheckInt(r.FormValue("department_id"))
	if err == nil && department_id > 0 {
		attrSql += " and a.department_id = ?"
		params = append(params, department_id)
	}

	// 角色ID
	role_id, err := common.CheckInt(r.FormValue("role_id"))
	if err == nil && role_id > 0 {
		attrSql += " and c.id = ?"
		params = append(params, role_id)
	}

	// 姓名匹配
	key := r.FormValue("key")
	if key != "" {
		if _, err := strconv.Atoi(key); err == nil {
			attrSql += " and a.phone like ?"
		} else {
			attrSql += " and a.NAME like ?"
		}
		params = append(params, "%"+key+"%")
	}

	// 查询总数
	countSql := `
		SELECT COUNT(DISTINCT a.id) as count
		FROM
			rbac_user AS a
			LEFT JOIN rbac_user_roles AS b ON a.id = b.user_id
			LEFT JOIN rbac_role AS c ON c.id = b.role_id
			left join department as d on a.department_id = d.id
			left join department as e on d.pid = e.id
			left join department as f on e.pid = f.id
		WHERE 1
		` + attrSql

	var count int
	err = database.GetOne(countSql, &count, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据总数查询失败",
			"err":  err.Error(),
			"sql":  common.DebugSql(countSql, params...),
		})
		return
	}

	type User struct {
		ID                int    `db:"id"`
		Phone             string `db:"phone"`
		Name              string `db:"NAME"`
		Status            int    `db:"STATUS"`
		Role_id           string `db:"role_id"`
		Role              string `db:"role"`
		ConsultationFee   string `db:"consultationFee"`
		ProfessionalTitle string `db:"professionalTitle"`
		Introduction      string `db:"introduction"`
		Proficient        string `db:"proficient"`
		Department_id     int    `db:"department_id"`
		Sort              int    `db:"sort"`
		Openid            string `db:"openid"`
		Is_recommend      int    `db:"is_recommend"`
	}

	// 查询数据
	dataSql := `
		SELECT
			a.id,
			a.phone,
			a.NAME,
			a.STATUS,
			a.department_id,
			a.is_recommend,
			a.sort,
			a.openid,
			IFNULL(GROUP_CONCAT(DISTINCT c.id SEPARATOR ','), -1) AS role_id,
			IFNULL(GROUP_CONCAT(DISTINCT c.role SEPARATOR ','), '角色未绑') AS role,
			IFNULL(a.consultationFee, 0) consultationFee,
			IFNULL(a.professionalTitle, '无') professionalTitle,
			IFNULL(a.introduction, '无') introduction,
			IFNULL(a.proficient, '无') proficient
		FROM
			rbac_user AS a
			LEFT JOIN rbac_user_roles AS b ON a.id = b.user_id
			LEFT JOIN rbac_role AS c ON c.id = b.role_id
			left join department as d on a.department_id = d.id
			left join department as e on d.pid = e.id
			left join department as f on e.pid = f.id
		WHERE 1
		` + attrSql + `
		GROUP BY
			a.id, a.phone, a.NAME, a.STATUS, a.consultationFee, a.professionalTitle, a.introduction, a.proficient,a.is_recommend
		order by 
		case when a.id=1 then 0 else 1 end,
		f.sort desc,e.sort desc,d.sort desc,a.sort desc, a.id desc
		LIMIT ? OFFSET ?
		`

	queryParams := append(params, limit, offset)
	var user []User
	err = database.GetAll(dataSql, &user, queryParams...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":  500,
			"msg":   "数据库查询失败",
			"error": err.Error(),
			"sql":   common.DebugSql(dataSql, queryParams...),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":  200,
		"data":  user,
		"count": count,
		// "sql":   common.DebugSql(dataSql, queryParams...),
	})
}

// user用户列表
func User_list(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}

	// 主逻辑开始
	attrSql := ""

	//ID
	id, err := common.CheckInt(r.FormValue("id"))
	if err == nil && id > 0 {
		attrSql += " and a.id = " + strconv.Itoa(id)
	}

	// 部门（科室）ID
	department_id, err := common.CheckInt(r.FormValue("department_id"))
	if err == nil && department_id > 0 {
		attrSql += " and a.department_id = " + strconv.Itoa(department_id)
	}

	// 角色ID
	role_id, err := common.CheckInt(r.FormValue("role_id"))
	if err == nil && role_id > 0 {
		attrSql += " and c.id = " + strconv.Itoa(role_id)
	}

	// 姓名匹配
	name := r.FormValue("name")
	if name != "" {
		attrSql += " and a.NAME like '%" + name + "%'"
	}

	type User struct {
		ID                int    `db:"id"`
		Phone             string `db:"phone"`
		Name              string `db:"NAME"`
		Status            int    `db:"STATUS"`
		Role_id           string `db:"role_id"`
		Role              string `db:"role"`
		ConsultationFee   string `db:"consultationFee"`
		ProfessionalTitle string `db:"professionalTitle"`
		Introduction      string `db:"introduction"`
		Proficient        string `db:"proficient"`
		Department_id     int    `db:"department_id"`
		Sort              int    `db:"sort"`
		Openid            string `db:"openid"`
		Is_recommend      int    `db:"is_recommend"`
	}
	sql := `
		SELECT
			a.id,
			a.phone,
			a.NAME,
			a.STATUS,
			a.department_id,
			a.is_recommend,
			a.sort,
			a.openid,
			IFNULL(GROUP_CONCAT(DISTINCT c.id SEPARATOR ','), -1) AS role_id,
			IFNULL(GROUP_CONCAT(DISTINCT c.role SEPARATOR ','), '角色未绑') AS role,
			IFNULL(a.consultationFee, 0) consultationFee,
			IFNULL(a.professionalTitle, '无') professionalTitle,
			IFNULL(a.introduction, '无') introduction,
			IFNULL(a.proficient, '无') proficient
		FROM
			rbac_user AS a
			LEFT JOIN rbac_user_roles AS b ON a.id = b.user_id
			LEFT JOIN rbac_role AS c ON c.id = b.role_id
			left join department as d on a.department_id = d.id
			left join department as e on d.pid = e.id
			left join department as f on e.pid = f.id
		WHERE 1
		` + attrSql + `
		GROUP BY
			a.id, a.phone, a.NAME, a.STATUS, a.consultationFee, a.professionalTitle, a.introduction, a.proficient,a.is_recommend
		order by 
		case when a.id=1 then 0 else 1 end,
		f.sort desc,e.sort desc,d.sort desc,a.sort desc, a.id desc
		`
	var user []User
	err = database.GetAll(sql, &user)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":  500,
			"msg":   "数据库查询失败",
			"error": err.Error(),
			// "sql":   common.DebugSql(sql),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": user,
		// "sql":  common.DebugSql(sql),
	})
}

// 添加用户
func User_add(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	phone, err := common.CheckPhone(r.FormValue("phone"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	//检测该手机号码是否已存在系统中
	sql := "SELECT id FROM rbac_user WHERE phone = ?"
	var id int
	err = database.GetOne(sql, &id, phone)
	if err == nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "该手机号码已存在系统中",
		})
		return
	}
	//查询当前数据最大的sort+10
	sql = "SELECT IFNULL(max(sort),10) + 10 sort FROM rbac_user"
	var sort int
	err = database.GetOne(sql, &sort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "查询最大排序值时出错",
			"err":  err.Error(),
			"sql":  common.DebugSql(sql),
		})
		return
	}

	name, err := common.CheckStr(r.FormValue("name"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	pwd, err := common.CheckPassword(r.FormValue("pwd"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	pwd = common.Md5Hash(pwd)

	sql = "insert into rbac_user (phone,name,pwd,sort) values (?,?,?,?)"
	result, err := database.Query(sql, phone, name, pwd, sort)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":  500,
			"msg":   "用户添加失败",
			"sql":   common.DebugSql(sql, phone, name, pwd, sort),
			"error": err.Error(),
		})
		return
	}
	new_id, _ := result.LastInsertId()
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code":   200,
		"msg":    "用户添加成功，请绑定用户角色及完善资料。",
		"new_id": new_id,
	})
	common.Add_log(fmt.Sprintf("添加用户：%s", name), r)
}

// 用户编辑
func User_edit(w http.ResponseWriter, r *http.Request) {
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	phoneValue := r.FormValue("phone")
	nameValue := r.FormValue("name")
	pwdValue := r.FormValue("pwd")
	consultationFeeValue := r.FormValue("consultationFee")
	professionalTitleValue := r.FormValue("professionalTitle")
	introductionValue := r.FormValue("introduction")
	proficientValue := r.FormValue("proficient")
	sortValue := r.FormValue("sort")
	statusValue := r.FormValue("status")
	departmentIdValue := r.FormValue("department_id") // 新增部门ID字段
	isRecommendValue := r.FormValue("is_recommend")   // 新增是否推荐字段

	// 构建更新 SQL
	sql := "UPDATE rbac_user SET "
	params := []interface{}{}

	if phoneValue != "" {
		phone, err := common.CheckPhone(phoneValue)
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		sql += "phone = ?, "
		params = append(params, phone)
	}

	if nameValue != "" {
		name, err := common.CheckStr(nameValue)
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		sql += "name = ?, "
		params = append(params, name)
	}

	if pwdValue != "" {
		pwd, err := common.CheckPassword(pwdValue)
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		pwd = common.Md5Hash(pwd)
		sql += "pwd = ?, "
		params = append(params, pwd)
	}

	if consultationFeeValue != "" {
		consultationFee := consultationFeeValue // 获取咨询费用
		sql += "consultationFee = ?, "
		params = append(params, consultationFee) //新增字段
	}

	if professionalTitleValue != "" {
		professionalTitle := professionalTitleValue // 获取职称
		sql += "professionalTitle = ?, "
		params = append(params, professionalTitle)
	}

	if introductionValue != "" {
		introduction := introductionValue // 获取简介
		sql += "introduction = ?, "
		params = append(params, introduction)
	}

	if proficientValue != "" {
		proficient := proficientValue // 获取擅长领域
		sql += "proficient = ?, "
		params = append(params, proficient)
	}

	if sortValue != "" {
		sort, err := common.CheckInt(sortValue) // 获取排序
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		sql += "sort = ?, "
		params = append(params, sort)
	}

	if statusValue != "" {
		status, err := common.CheckInt(statusValue) // 获取状态
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		sql += "status = ?, "
		params = append(params, status)
	}
	if departmentIdValue != "" {
		departmentId, err := common.CheckInt(departmentIdValue) // 获取部门ID
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		sql += "department_id = ?, "
		params = append(params, departmentId)
	}

	if isRecommendValue != "" {
		isRecommend, err := common.CheckInt(isRecommendValue) // 获取是否推荐
		if err != nil {
			common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
				"code": 500,
				"msg":  err.Error(),
			})
			return
		}
		sql += "is_recommend = ?, "
		params = append(params, isRecommend)
	}
	// 移除最后的逗号和空格
	if len(params) == 0 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "没有要更新的字段",
		})
		return
	}
	sql = sql[:len(sql)-2] // 去掉最后的逗号和空格
	sql += " WHERE id = ?"
	params = append(params, id)

	// 执行更新
	result, err := database.Query(sql, params...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据更新失败",
		})
		return
	}
	// 上传用户头像
	is_avatar_uploaded := false
	avatar_file := r.MultipartForm.File["avatar_file"]
	if len(avatar_file) > 0 {
		isImage, fileData, err := common.Check_Is_Image(avatar_file[0])
		if isImage && err == nil {
			filepath := config.Dist_catagory + "/uploads/icons/avatarurl_" + strconv.Itoa(int(id)) + ".png"
			if outFile, err := os.OpenFile(filepath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0666); err == nil {
				defer outFile.Close()
				if _, writeErr := outFile.Write(fileData); writeErr == nil {
					is_avatar_uploaded = true
				}
			}
		}
	}
	//上传用户签名
	is_sign_uploaded := false
	sign_file := r.MultipartForm.File["sign_file"]
	if len(sign_file) > 0 {
		isImage, fileData, err := common.Check_Is_Image(sign_file[0])
		if isImage && err == nil {
			filepath := config.Dist_catagory + "/uploads/icons/sign_" + strconv.Itoa(int(id)) + ".png"
			if outFile, err := os.OpenFile(filepath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0666); err == nil {
				defer outFile.Close()
				if _, writeErr := outFile.Write(fileData); writeErr == nil {
					is_sign_uploaded = true
				}
			}
		}
	}
	//上传用户签名
	is_barcode_uploaded := false
	barcode_file := r.MultipartForm.File["barcode_file"]
	if len(barcode_file) > 0 {
		isImage, fileData, err := common.Check_Is_Image(barcode_file[0])
		if isImage && err == nil {
			filepath := config.Dist_catagory + "/uploads/icons/barcode_" + strconv.Itoa(int(id)) + ".png"
			if outFile, err := os.OpenFile(filepath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0666); err == nil {
				defer outFile.Close()
				if _, writeErr := outFile.Write(fileData); writeErr == nil {
					is_barcode_uploaded = true
				}
			}
		}
	}

	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(
		w, http.StatusOK, map[string]interface{}{
			"code":              200,
			"msg":               "操作执行成功",
			"is_upload_avatar":  is_avatar_uploaded,
			"is_upload_sign":    is_sign_uploaded,
			"is_upload_barcode": is_barcode_uploaded,
			"RowsAffected":      RowsAffected,
		},
	)
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("修改用户：%d", id), r)
	}
}

// 用户删除
func User_del(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	if id == 1 {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "不能删除超级管理员",
		})
		return
	}
	// 为防止误删除，这里限制只能删除 id > 12 的用户
	sql := "DELETE FROM rbac_user WHERE id = ? and id > 12"
	result, err := database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据删除失败",
		})
		return
	}
	RowsAffected, _ := result.RowsAffected()
	common.JSONResponse(
		w, http.StatusOK, map[string]interface{}{
			"code":         200,
			"msg":          "操作执行成功",
			"RowsAffected": RowsAffected,
		},
	)
	if RowsAffected > 0 {
		common.Add_log(fmt.Sprintf("删除用户：%d", id), r)
	}
}

// 用户绑定角色
func User_bind_roles(w http.ResponseWriter, r *http.Request) {
	// 超级管理员鉴权
	api_id := config.SystemPerm
	if _, isLogin := common.Check_Perm(w, r, api_id); !isLogin {
		return
	}
	// 主逻辑开始
	id, err := common.CheckInt(r.FormValue("id"))
	if id < 1 || err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	role_ids, err := common.CheckStr(r.FormValue("role_ids"))
	if err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}
	role_ids_array := strings.Split(role_ids, ",")
	if slices.Contains(role_ids_array, "1") { // Go 1.21+ slices.Contains
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "对不起，您选的角色中有超级管理员，这是不被允许的。",
		})
		return
	}

	// 互斥角色ID检查
	exclusiveRoleIds := []string{"3", "4", "9"} // 医助、医生、售后
	hasExclusiveRole := false
	hasOtherRole := false
	multipleExclusiveRoles := false

	// 检查是否包含互斥角色
	exclusiveRoleCount := 0
	for _, roleID := range role_ids_array {
		if slices.Contains(exclusiveRoleIds, roleID) {
			hasExclusiveRole = true
			exclusiveRoleCount++
		} else {
			hasOtherRole = true
		}
	}

	if exclusiveRoleCount > 1 {
		multipleExclusiveRoles = true
	}

	// 如果同时选择了互斥角色和其他角色，返回错误
	if hasExclusiveRole && hasOtherRole {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "带星号的角色只允许选择1个",
		})
		return
	}

	// 如果选择了多个互斥角色，返回错误
	if multipleExclusiveRoles {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 400,
			"msg":  "带星号的角色只允许选择1个",
		})
		return
	}

	sql := "DELETE FROM rbac_user_roles WHERE user_id = ?"
	_, err = database.Query(sql, id)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据绑定前的删除失败",
		})
		return
	}

	var args []interface{}
	for _, role_id := range role_ids_array {
		args = append(args, id, role_id)
	}
	valuesStr := strings.Repeat("(?, ?),", len(role_ids_array))
	valuesStr = valuesStr[:len(valuesStr)-1] // 去掉最后一个逗号
	sql = fmt.Sprintf("INSERT INTO rbac_user_roles (user_id, role_id) VALUES %s", valuesStr)

	_, err = database.Query(sql, args...)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "数据绑定失败",
			"err":  err.Error(),
		})
		return
	}

	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "数据绑定成功",
	})
	common.Add_log(fmt.Sprintf("用户绑定角色：%d", id), r)
}

// 用户退出登录
func User_logout(w http.ResponseWriter, r *http.Request) {
	session, _ := common.SessionStore.Get(r, "admin_sessions")
	session.Options.MaxAge = -1
	session.Save(r, w)
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"msg":  "退出登录成功",
	})
}

// 用户登录
func User_login(w http.ResponseWriter, r *http.Request) {
	session, _ := common.SessionStore.Get(r, "admin_sessions")
	// 检测是否为POST
	if r.Method != http.MethodPost {
		http.Error(w, "Invalid request method", http.StatusMethodNotAllowed)
		return
	}

	// // 获取 Authorization 请求头
	// authHeader := r.Header.Get("Authorization")
	// if !strings.HasPrefix(authHeader, "Basic ") {
	//  http.Error(w, "Unauthorized", http.StatusUnauthorized)
	//  return
	// }

	// // 提取 Base64 编码的凭证部分
	// encodedCredentials := strings.TrimPrefix(authHeader, "Basic ")
	// decodedCredentials, err := base64.StdEncoding.DecodeString(encodedCredentials)
	// if err != nil {
	//  http.Error(w, "Unauthorized", http.StatusUnauthorized)
	//  return
	// }

	// // 解析用户名和密码
	// credentials := strings.SplitN(string(decodedCredentials), ":", 2)
	// if len(credentials) != 2 {
	//  http.Error(w, "Unauthorized", http.StatusUnauthorized)
	//  return
	// }
	// phone := credentials[0]
	// pwd := credentials[1]

	phone, phone_err := common.CheckStr(r.FormValue("phone"))
	pwd, pwd_err := common.CheckStr(r.FormValue("pwd"))
	if phone_err != nil || pwd_err != nil {
		common.JSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"code": 500,
			"msg":  "用户名或密码数据格式出错",
		})
		return
	}
	pwd = common.Md5Hash(pwd)
	type User struct {
		Id            int    `db:"id"`
		Phone         string `db:"phone"`
		Name          string `db:"name"`
		Status        int    `db:"status"`
		Department_id int    `db:"department_id"`
		Role_ids      string `db:"role_ids"`
		Roles         string `db:"roles"`
		Perm_ids      string `db:"perm_ids"`
		Dep_ids       string `db:"dep_ids"`
	}
	var user User
	sql := `
	SELECT
	  u.id,
	  u.phone,
	  u.name,
	  u.status,
	  u.department_id,
	  IFNULL(GROUP_CONCAT(DISTINCT r.id),0) AS role_ids,
	  IFNULL(GROUP_CONCAT(DISTINCT r.role),'未绑定角色') AS roles,
	  IFNULL(GROUP_CONCAT(DISTINCT p.id), 0) AS perm_ids,
	  ifnull(dep_ids, 0) AS dep_ids
	FROM
	  rbac_user AS u
	  LEFT JOIN rbac_user_roles ur ON ur.user_id = u.id
	  LEFT JOIN rbac_role r ON r.id = ur.role_id
	  LEFT JOIN rbac_role_perm rp ON rp.role_id = r.id
	  LEFT JOIN rbac_perm p ON rp.perm_id = p.id 
	  LEFT JOIN salemaster_department sd ON sd.user_id = u.id
	WHERE
	  u.phone = ?
	  AND u.pwd = ?
	GROUP BY
	  u.id 
	ORDER BY
	  u.id
`
	err := database.GetRow(sql, &user, phone, pwd)
	if err != nil {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code":    500,
			"msg":     "用户名或密码错误",
			"errdata": err.Error(),
			// "sql":     common.DebugSql(sql, phone, pwd),
		})
		return
	}
	user_status := user.Status
	if user_status == 0 {
		common.JSONResponse(w, http.StatusForbidden, map[string]interface{}{
			"code": 403,
			"msg":  "用户被禁用",
		})
		return
	}
	// id,phone,name,role_id,role,perm_id,perm
	match, _ := regexp.MatchString(`^[\d,]+$`, user.Role_ids)
	if !match {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "角色id格式不正确",
		})
		return
	}
	match, _ = regexp.MatchString(`^[\d,]+$`, user.Perm_ids)
	if !match {
		common.JSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"code": 500,
			"msg":  "权限id格式不正确",
		})
		return
	}

	// 加一层安全检测
	session.Values["id"] = user.Id
	session.Values["name"] = user.Name
	session.Values["phone"] = phone
	session.Values["roles"] = user.Roles
	session.Values["role_ids"] = user.Role_ids
	session.Values["perm_ids"] = user.Perm_ids
	session.Values["department_id"] = user.Department_id
	session.Values["dep_ids"] = user.Dep_ids // 部门ID
	session.Save(r, w)

	// 将结果放入响应
	user.Phone = phone
	common.JSONResponse(w, http.StatusOK, map[string]interface{}{
		"code": 200,
		"data": user,
		"msg":  "登录成功",
		// "sql":  common.DebugSql(sql, phone, pwd),
	})
}
